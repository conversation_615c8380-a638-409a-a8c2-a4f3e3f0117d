Log started at 7/5/2025 1:37:21 PM
2025-07-05 13:37:21.154 [Information] LoggingService: Logging service initialized
2025-07-05 13:37:21.180 [Information] App: Starting integrated application initialization
2025-07-05 13:37:21.182 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-05 13:37:21.187 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 13:37:21.190 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 13:37:21.190 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 13:37:21.193 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 13:37:21.196 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 13:37:21.201 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 13:37:21.221 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 13:37:21.230 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 13:37:21.234 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 13:37:21.235 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 13:37:21.240 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 13:37:21.244 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 13:37:21.248 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 13:37:21.248 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 13:37:21.254 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 13:37:21.259 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 13:37:21.264 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 13:37:21.265 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 13:37:21.271 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 13:37:21.275 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 13:37:21.278 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 13:37:21.279 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 13:37:21.282 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 13:37:21.286 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 13:37:21.287 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 13:37:21.291 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-05 13:37:21.292 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 13:37:21.294 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-05 13:37:21.295 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 13:37:21.295 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 13:37:21.298 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-05 13:37:21.298 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 13:37:21.300 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-05 13:37:21.301 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 13:37:21.302 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 13:37:21.302 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 13:37:21.312 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-05 13:37:21.313 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-05 13:37:21.313 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-05 13:37:21.322 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-05 13:37:21.322 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-05 13:37:21.325 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 13:37:21.327 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 13:37:21.333 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 13:37:21.336 [Information] LibraryExtractor: Copying system libraries
2025-07-05 13:37:21.351 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 13:37:21.375 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 13:37:46.337 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-05 13:38:28.490 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-05 13:39:44.621 [Information] LibraryExtractor: Verifying library extraction
2025-07-05 13:39:44.622 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-05 13:39:44.622 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-05 13:39:44.623 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-05 13:39:44.623 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-05 13:39:44.624 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-05 13:39:44.631 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-05 13:39:44.635 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-05 13:39:44.638 [Information] DependencyManager: Initializing dependency manager
2025-07-05 13:39:44.640 [Information] DependencyManager: Setting up library search paths
2025-07-05 13:39:44.642 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 13:39:44.643 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 13:39:44.643 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 13:39:44.644 [Information] DependencyManager: Updated PATH environment variable
2025-07-05 13:39:44.647 [Information] DependencyManager: Verifying required directories
2025-07-05 13:39:44.647 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 13:39:44.648 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 13:39:44.649 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-05 13:39:44.652 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 13:39:44.655 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-05 13:39:44.677 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-05 13:39:44.692 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-05 13:39:44.698 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-05 13:39:44.706 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 13:39:44.708 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 13:39:44.711 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 13:39:44.712 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 13:39:44.714 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 13:39:44.716 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-05 13:39:44.721 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-05 13:39:44.722 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 13:39:44.723 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-05 13:39:44.724 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 13:39:44.725 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 13:39:44.728 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-05 13:39:44.730 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-05 13:39:44.730 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-05 13:39:44.731 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-05 13:39:44.732 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-05 13:39:44.734 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-05 13:39:44.735 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-05 13:39:44.736 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-05 13:39:45.412 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-05 13:39:45.413 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-05 13:39:45.416 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-05 13:39:45.417 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-05 13:39:45.418 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-05 13:39:45.419 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-05 13:39:45.419 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-05 13:39:45.421 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-05 13:39:45.422 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-05 13:39:45.423 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 13:39:45.425 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 13:39:45.426 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-05 13:39:45.427 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-05 13:39:45.428 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-05 13:39:45.429 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 13:39:45.430 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 13:39:45.431 [Information] DependencyManager: Setting up environment variables
2025-07-05 13:39:45.431 [Information] DependencyManager: Environment variables configured
2025-07-05 13:39:45.434 [Information] DependencyManager: Verifying library loading status
2025-07-05 13:39:46.005 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-05 13:39:46.006 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-05 13:39:46.006 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-05 13:39:46.013 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-05 13:39:46.016 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-05 13:39:46.030 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-05 13:39:46.033 [Information] IntegratedStartupService: Verifying system readiness
2025-07-05 13:39:46.034 [Information] IntegratedStartupService: System readiness verification passed
2025-07-05 13:39:46.035 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-05 13:39:46.037 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-05 13:39:46.038 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 13:39:46.038 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 13:39:46.038 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 13:39:46.039 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-05 13:39:46.039 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-05 13:39:46.039 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-05 13:39:46.040 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 13:39:46.040 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-05 13:39:46.041 [Information] App: Integrated startup completed successfully
2025-07-05 13:39:46.045 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-05 13:39:46.395 [Information] App: Initializing application services
2025-07-05 13:39:46.398 [Information] AppConfigurationService: Initializing configuration service
2025-07-05 13:39:46.399 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 13:39:46.536 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 13:39:46.538 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-05 13:39:46.539 [Information] App: Configuration service initialized successfully
2025-07-05 13:39:46.542 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-05 13:39:46.542 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-05 13:39:46.555 [Information] App: Environment variable exists: True, not 'false': False
2025-07-05 13:39:46.556 [Information] App: Final useDummyImplementations value: False
2025-07-05 13:39:46.556 [Information] App: Updating config to NOT use dummy implementations
2025-07-05 13:39:46.559 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-05 13:39:46.593 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 13:39:46.594 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-05 13:39:46.595 [Information] App: usePatchedImplementation flag is: True
2025-07-05 13:39:46.596 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-05 13:39:46.596 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-05 13:39:46.596 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-05 13:39:46.600 [Information] App: verboseLogging flag is: True
2025-07-05 13:39:46.604 [Information] App: Verifying real hardware requirements...
2025-07-05 13:39:46.604 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-05 13:39:46.605 [Information] App: ✓ Found critical library: apci.dll
2025-07-05 13:39:46.605 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-05 13:39:46.606 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-05 13:39:46.607 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 13:39:46.608 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-05 13:39:46.609 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-05 13:39:46.609 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-05 13:39:46.621 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-07-05 13:39:46.622 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-07-05 13:39:46.624 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-05 13:39:46.626 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-07-05 13:39:46.626 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoFlashWR.Communication.dll
2025-07-05 13:39:46.676 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-05 13:39:46.677 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\patched_factory_created.txt
2025-07-05 13:39:46.677 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-07-05 13:39:46.677 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-07-05 13:39:46.678 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-05 13:39:46.738 [Information] App: Creating Vocom service (attempt 1/3)
2025-07-05 13:39:46.744 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-05 13:39:46.744 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-05 13:39:46.746 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-05 13:39:46.746 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-05 13:39:46.752 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-05 13:39:46.753 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-05 13:39:46.753 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-05 13:39:46.754 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 13:39:46.754 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-05 13:39:46.755 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-05 13:39:46.755 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-05 13:39:46.758 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-05 13:39:46.762 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-05 13:39:46.776 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-05 13:39:46.778 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-05 13:39:46.779 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-05 13:39:46.803 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-05 13:39:46.857 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-05 13:39:46.859 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-05 13:39:46.859 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-05 13:39:46.861 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 13:39:46.862 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll has incompatible architecture
2025-07-05 13:39:46.862 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 13:39:46.863 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll has incompatible architecture
2025-07-05 13:39:47.065 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 13:39:47.067 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-05 13:39:47.068 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-05 13:39:47.068 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-05 13:39:47.069 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-05 13:39:47.072 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-05 13:39:47.072 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-05 13:39:47.073 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-05 13:39:47.077 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-05 13:39:47.078 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-05 13:39:47.079 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 13:39:47.080 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-05 13:39:47.080 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-05 13:39:47.083 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-05 13:39:47.084 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-05 13:39:47.085 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-05 13:39:47.085 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-05 13:39:47.086 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-05 13:39:47.086 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-05 13:39:47.086 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-05 13:39:47.088 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-05 13:39:47.088 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-05 13:39:47.088 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-05 13:39:47.091 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-05 13:39:47.092 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-05 13:39:47.092 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-05 13:39:47.093 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 13:39:47.094 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-07-05 13:39:47.098 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-07-05 13:39:47.098 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-05 13:39:47.099 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-07-05 13:39:47.100 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-05 13:39:47.101 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-05 13:39:47.102 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-05 13:39:47.103 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-05 13:39:47.106 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-05 13:39:47.111 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-05 13:39:47.111 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-05 13:39:47.122 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-05 13:39:47.123 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-05 13:39:47.123 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 13:39:47.124 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 13:39:47.128 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-05 13:39:47.132 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 13:39:47.133 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 13:39:47.134 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-05 13:39:47.135 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 13:39:47.136 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 13:39:47.137 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-05 13:39:47.139 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 13:39:47.140 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-05 13:39:47.141 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 13:39:47.142 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-05 13:39:47.143 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 13:39:47.144 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 13:39:47.145 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-05 13:39:47.146 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 13:39:47.148 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 13:39:47.149 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-05 13:39:47.150 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 13:39:47.152 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 13:39:47.154 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 13:39:47.155 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 13:39:47.156 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-05 13:39:47.156 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 13:39:47.158 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 13:39:47.159 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-05 13:39:47.160 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 13:39:47.162 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 13:39:47.162 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-05 13:39:47.164 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 13:39:47.166 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 13:39:47.166 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-05 13:39:47.168 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 13:39:47.485 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 13:39:47.487 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 13:39:47.493 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-05 13:39:47.493 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 13:39:47.494 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-07-05 13:39:47.495 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-05 13:39:47.495 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-05 13:39:47.498 [Information] VocomDriver: Initializing Vocom driver
2025-07-05 13:39:47.501 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-05 13:39:47.506 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-05 13:39:47.507 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 13:39:47.508 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 13:39:47.509 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 13:39:47.510 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-05 13:39:47.513 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-05 13:39:47.514 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-05 13:39:47.518 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-05 13:39:47.518 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-05 13:39:47.519 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-05 13:39:47.520 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 13:39:47.526 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-07-05 13:39:47.530 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-07-05 13:39:47.535 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-07-05 13:39:47.537 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-05 13:39:47.537 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-05 13:39:47.538 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-05 13:39:47.540 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-05 13:39:47.541 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-05 13:39:47.542 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-05 13:39:47.542 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-05 13:39:47.543 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-05 13:39:47.543 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-05 13:39:47.543 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-05 13:39:47.544 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-05 13:39:47.544 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-05 13:39:47.545 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-05 13:39:47.545 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-05 13:39:47.545 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-05 13:39:47.546 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-05 13:39:47.546 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-05 13:39:47.546 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-05 13:39:47.547 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-05 13:39:47.547 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-05 13:39:47.548 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-05 13:39:47.548 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-05 13:39:47.548 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-05 13:39:47.549 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-05 13:39:47.549 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-05 13:39:47.550 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-05 13:39:47.550 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-05 13:39:47.550 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-05 13:39:47.551 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-05 13:39:47.551 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-05 13:39:47.552 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-05 13:39:47.552 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-05 13:39:47.552 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-05 13:39:47.553 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-05 13:39:47.553 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-05 13:39:47.553 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-05 13:39:47.554 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-05 13:39:47.554 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-05 13:39:47.554 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-05 13:39:47.555 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-05 13:39:47.555 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-05 13:39:47.556 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-05 13:39:47.556 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-05 13:39:47.557 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-05 13:39:47.557 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-05 13:39:47.557 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-05 13:39:47.559 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-05 13:39:47.561 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-05 13:39:47.562 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-05 13:39:47.562 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-05 13:39:47.563 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-05 13:39:47.563 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-05 13:39:47.564 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-05 13:39:47.564 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-05 13:39:47.575 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-05 13:39:47.576 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-05 13:39:47.578 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-05 13:39:47.581 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-05 13:39:47.770 [Information] WiFiCommunicationService: WiFi is available
2025-07-05 13:39:47.771 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-05 13:39:47.774 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-05 13:39:47.776 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-05 13:39:47.779 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-05 13:39:47.781 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-05 13:39:47.787 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-05 13:39:47.789 [Information] VocomService: Initializing enhanced Vocom services
2025-07-05 13:39:47.791 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-05 13:39:47.794 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-05 13:39:47.804 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-05 13:39:47.805 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-05 13:39:47.805 [Information] VocomService: Native USB communication service initialized
2025-07-05 13:39:47.806 [Information] VocomService: Enhanced device detection service initialized
2025-07-05 13:39:47.807 [Information] VocomService: Connection recovery service initialized
2025-07-05 13:39:47.807 [Information] VocomService: Enhanced services initialization completed
2025-07-05 13:39:47.811 [Information] VocomService: Checking if PTT application is running
2025-07-05 13:39:47.835 [Information] VocomService: PTT application is not running
2025-07-05 13:39:47.837 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 13:39:47.840 [Debug] VocomService: Bluetooth is enabled
2025-07-05 13:39:47.842 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-05 13:39:47.842 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-05 13:39:47.843 [Information] App: Initializing Vocom service
2025-07-05 13:39:47.843 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-05 13:39:47.844 [Information] VocomService: Initializing enhanced Vocom services
2025-07-05 13:39:47.844 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-05 13:39:47.846 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-05 13:39:47.847 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-05 13:39:47.847 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-05 13:39:47.848 [Information] VocomService: Native USB communication service initialized
2025-07-05 13:39:47.848 [Information] VocomService: Enhanced device detection service initialized
2025-07-05 13:39:47.849 [Information] VocomService: Connection recovery service initialized
2025-07-05 13:39:47.849 [Information] VocomService: Enhanced services initialization completed
2025-07-05 13:39:47.850 [Information] VocomService: Checking if PTT application is running
2025-07-05 13:39:47.870 [Information] VocomService: PTT application is not running
2025-07-05 13:39:47.871 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 13:39:47.872 [Debug] VocomService: Bluetooth is enabled
2025-07-05 13:39:47.873 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-05 13:39:47.878 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 13:39:47.879 [Information] VocomService: Using new enhanced device detection service
2025-07-05 13:39:47.882 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 13:39:47.885 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 13:39:48.805 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-05 13:39:48.807 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 13:39:48.808 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 13:39:48.811 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-05 13:39:48.811 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-05 13:39:48.814 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-05 13:39:48.818 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-05 13:39:48.822 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-05 13:39:49.314 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-05 13:39:49.318 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-05 13:39:49.322 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-05 13:39:49.322 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-05 13:39:49.324 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-05 13:39:49.324 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-05 13:39:49.325 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-05 13:39:49.325 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 13:39:49.328 [Debug] VocomService: Bluetooth is enabled
2025-07-05 13:39:49.331 [Debug] VocomService: Checking if WiFi is available
2025-07-05 13:39:49.334 [Debug] VocomService: WiFi is available
2025-07-05 13:39:49.335 [Information] VocomService: Found 3 Vocom devices
2025-07-05 13:39:49.335 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-05 13:39:49.342 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-05 13:39:49.342 [Information] VocomService: Checking if PTT application is running
2025-07-05 13:39:49.363 [Information] VocomService: PTT application is not running
2025-07-05 13:39:49.375 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-05 13:39:49.375 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-05 13:39:49.376 [Information] VocomService: Checking if PTT application is running
2025-07-05 13:39:49.397 [Information] VocomService: PTT application is not running
2025-07-05 13:39:49.403 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-05 13:39:49.405 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-05 13:39:49.407 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2
2025-07-05 13:39:49.408 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-05 13:39:49.408 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-05 13:39:49.462 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-05 13:39:49.465 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-05 13:39:49.467 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-05 13:39:49.471 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-05 13:39:49.473 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-05 13:39:49.474 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-05 13:39:49.474 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-05 13:39:49.474 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 13:39:49.475 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 13:39:49.475 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-05 13:39:49.481 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-05 13:39:49.488 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 13:39:49.489 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-05 13:39:49.497 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 13:39:49.501 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 13:39:49.502 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 13:39:49.506 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 13:39:49.510 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 13:39:49.527 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 13:39:49.533 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 13:39:49.543 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 13:39:49.560 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 13:39:49.565 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 13:39:49.566 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 13:39:49.571 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 13:39:49.572 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 13:39:49.573 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 13:39:49.575 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 13:39:49.576 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 13:39:49.576 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 13:39:49.580 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 13:39:49.580 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 13:39:49.581 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 13:39:49.584 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 13:39:49.585 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 13:39:49.585 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 13:39:49.586 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 13:39:49.592 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 13:39:49.594 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 13:39:49.595 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 13:39:49.595 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 13:39:49.596 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 13:39:49.596 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 13:39:49.596 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 13:39:49.597 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 13:39:49.597 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 13:39:49.598 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 13:39:49.598 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 13:39:49.599 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 13:39:49.599 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-05 13:39:49.600 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-05 13:39:49.604 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-05 13:39:49.605 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 13:39:49.605 [Information] VocomService: Using new enhanced device detection service
2025-07-05 13:39:49.605 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 13:39:49.606 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 13:39:50.211 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-05 13:39:50.211 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 13:39:50.211 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 13:39:50.212 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-05 13:39:50.212 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-05 13:39:50.212 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-05 13:39:50.213 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-05 13:39:50.213 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-05 13:39:50.766 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-05 13:39:50.769 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-05 13:39:50.770 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-05 13:39:50.770 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-05 13:39:50.771 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-05 13:39:50.771 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-05 13:39:50.771 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-05 13:39:50.772 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 13:39:50.774 [Debug] VocomService: Bluetooth is enabled
2025-07-05 13:39:50.775 [Debug] VocomService: Checking if WiFi is available
2025-07-05 13:39:50.775 [Debug] VocomService: WiFi is available
2025-07-05 13:39:50.776 [Information] VocomService: Found 3 Vocom devices
2025-07-05 13:39:50.777 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 13:39:50.777 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-05 13:39:50.778 [Information] VocomService: Checking if PTT application is running
2025-07-05 13:39:50.797 [Information] VocomService: PTT application is not running
2025-07-05 13:39:50.797 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-05 13:39:50.798 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-05 13:39:50.798 [Information] VocomService: Checking if PTT application is running
2025-07-05 13:39:50.820 [Information] VocomService: PTT application is not running
2025-07-05 13:39:50.821 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-05 13:39:50.821 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-05 13:39:50.822 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2
2025-07-05 13:39:50.822 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-05 13:39:50.823 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-05 13:39:50.823 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-05 13:39:50.824 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-05 13:39:50.824 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-05 13:39:50.824 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-05 13:39:50.825 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-05 13:39:50.831 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-05 13:39:50.831 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-05 13:39:50.831 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 13:39:50.835 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 13:39:50.836 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 13:39:50.836 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 13:39:50.836 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 13:39:50.837 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-05 13:39:50.837 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-05 13:39:50.838 [Information] VocomService: Checking if PTT application is running
2025-07-05 13:39:50.862 [Information] VocomService: PTT application is not running
2025-07-05 13:39:50.868 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-05 13:39:50.869 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 13:39:50.872 [Debug] VocomService: Bluetooth is enabled
2025-07-05 13:39:50.874 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-05 13:39:51.682 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-05 13:39:51.685 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-05 13:39:51.686 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-05 13:39:51.687 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-05 13:39:51.687 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 13:39:51.689 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-05 13:39:51.690 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-05 13:39:51.691 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-05 13:39:51.697 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-05 13:39:51.700 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-05 13:39:51.706 [Information] BackupService: Initializing backup service
2025-07-05 13:39:51.706 [Information] BackupService: Backup service initialized successfully
2025-07-05 13:39:51.707 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-05 13:39:51.707 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-05 13:39:51.711 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-05 13:39:51.788 [Information] BackupService: Compressing backup data
2025-07-05 13:39:51.811 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-07-05 13:39:51.813 [Information] BackupServiceFactory: Created template for category: Production
2025-07-05 13:39:51.813 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-05 13:39:51.814 [Information] BackupService: Compressing backup data
2025-07-05 13:39:51.822 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (453 bytes)
2025-07-05 13:39:51.823 [Information] BackupServiceFactory: Created template for category: Development
2025-07-05 13:39:51.823 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-05 13:39:51.824 [Information] BackupService: Compressing backup data
2025-07-05 13:39:51.826 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (445 bytes)
2025-07-05 13:39:51.826 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-05 13:39:51.826 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-05 13:39:51.827 [Information] BackupService: Compressing backup data
2025-07-05 13:39:51.828 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-07-05 13:39:51.829 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-05 13:39:51.829 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-05 13:39:51.830 [Information] BackupService: Compressing backup data
2025-07-05 13:39:51.832 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (451 bytes)
2025-07-05 13:39:51.832 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-05 13:39:51.835 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-05 13:39:51.836 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-05 13:39:51.837 [Information] BackupService: Compressing backup data
2025-07-05 13:39:51.838 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-07-05 13:39:51.839 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-05 13:39:51.839 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-05 13:39:51.841 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-05 13:39:51.846 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 13:39:51.854 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 13:39:52.005 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 13:39:52.006 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 13:39:52.008 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-05 13:39:52.009 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-05 13:39:52.009 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-05 13:39:52.012 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-05 13:39:52.013 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-05 13:39:52.020 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-05 13:39:52.020 [Information] App: Flash operation monitor service initialized successfully
2025-07-05 13:39:52.047 [Information] LicensingService: Initializing licensing service
2025-07-05 13:39:52.159 [Information] LicensingService: License information loaded successfully
2025-07-05 13:39:52.164 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-05 13:39:52.165 [Information] App: Licensing service initialized successfully
2025-07-05 13:39:52.165 [Information] App: License status: Trial
2025-07-05 13:39:52.166 [Information] App: Trial period: 30 days remaining
2025-07-05 13:39:52.167 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-05 13:39:52.224 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-05 13:39:52.448 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-05 13:39:52.449 [Information] VocomService: Initializing enhanced Vocom services
2025-07-05 13:39:52.449 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-05 13:39:52.450 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-05 13:39:52.451 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-05 13:39:52.451 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-05 13:39:52.451 [Information] VocomService: Native USB communication service initialized
2025-07-05 13:39:52.452 [Information] VocomService: Enhanced device detection service initialized
2025-07-05 13:39:52.452 [Information] VocomService: Connection recovery service initialized
2025-07-05 13:39:52.452 [Information] VocomService: Enhanced services initialization completed
2025-07-05 13:39:52.453 [Information] VocomService: Checking if PTT application is running
2025-07-05 13:39:52.471 [Information] VocomService: PTT application is not running
2025-07-05 13:39:52.472 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 13:39:52.476 [Debug] VocomService: Bluetooth is enabled
2025-07-05 13:39:52.477 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-05 13:39:52.529 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 13:39:52.529 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 13:39:52.530 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 13:39:52.530 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 13:39:52.530 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 13:39:52.535 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 13:39:52.536 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 13:39:52.540 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 13:39:52.540 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 13:39:52.541 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 13:39:52.553 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 13:39:52.555 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 13:39:52.556 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 13:39:52.556 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 13:39:52.557 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 13:39:52.557 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 13:39:52.558 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 13:39:52.558 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 13:39:52.558 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 13:39:52.561 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 13:39:52.561 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 13:39:52.562 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 13:39:52.562 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 13:39:52.563 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 13:39:52.563 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 13:39:52.563 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 13:39:52.564 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 13:39:52.570 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 13:39:52.577 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 13:39:52.579 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 13:39:52.586 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 13:39:52.590 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 13:39:52.605 [Information] CANRegisterAccess: Read value 0x85 from register 0x0141 (simulated)
2025-07-05 13:39:52.608 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 13:39:52.610 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 13:39:52.610 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 13:39:52.620 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 13:39:52.620 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 13:39:52.626 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 13:39:52.627 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 13:39:52.627 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 13:39:52.636 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 13:39:52.637 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 13:39:52.637 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 13:39:52.645 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 13:39:52.646 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 13:39:52.653 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 13:39:52.654 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 13:39:52.660 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 13:39:52.661 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 13:39:52.673 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 13:39:52.673 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 13:39:52.679 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 13:39:52.680 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 13:39:52.686 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 13:39:52.687 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 13:39:52.693 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 13:39:52.694 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 13:39:52.704 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 13:39:52.705 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 13:39:52.711 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 13:39:52.712 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 13:39:52.720 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 13:39:52.720 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 13:39:52.726 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 13:39:52.727 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 13:39:52.737 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 13:39:52.774 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 13:39:52.779 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 13:39:52.780 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 13:39:52.786 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 13:39:52.787 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 13:39:52.793 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 13:39:52.794 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 13:39:52.800 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 13:39:52.801 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 13:39:52.807 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 13:39:52.808 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 13:39:52.808 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 13:39:52.818 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 13:39:52.819 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 13:39:52.819 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 13:39:52.820 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 13:39:52.826 [Information] CANRegisterAccess: Read value 0xF9 from register 0x0141 (simulated)
2025-07-05 13:39:52.832 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 13:39:52.838 [Information] CANRegisterAccess: Read value 0x86 from register 0x0141 (simulated)
2025-07-05 13:39:52.839 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 13:39:52.839 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 13:39:52.840 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 13:39:52.840 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 13:39:52.845 [Information] CANRegisterAccess: Read value 0xDA from register 0x0140 (simulated)
2025-07-05 13:39:52.846 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 13:39:52.846 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 13:39:52.847 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 13:39:52.847 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 13:39:52.859 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 13:39:52.861 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 13:39:52.861 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 13:39:52.871 [Information] VocomService: Sending data and waiting for response
2025-07-05 13:39:52.872 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-05 13:39:52.924 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-05 13:39:52.927 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 13:39:52.928 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 13:39:52.929 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 13:39:52.929 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 13:39:52.941 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 13:39:52.942 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 13:39:52.943 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 13:39:52.957 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 13:39:52.971 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 13:39:52.982 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 13:39:52.994 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 13:39:53.005 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 13:39:53.006 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 13:39:53.007 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 13:39:53.021 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 13:39:53.023 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 13:39:53.023 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 13:39:53.061 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 13:39:53.080 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 13:39:53.095 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 13:39:53.115 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 13:39:53.147 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 13:39:53.171 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 13:39:53.172 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 13:39:53.173 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 13:39:53.192 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 13:39:53.195 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 13:39:53.195 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 13:39:53.196 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 13:39:53.196 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 13:39:53.196 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 13:39:53.197 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 13:39:53.197 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 13:39:53.197 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 13:39:53.198 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 13:39:53.198 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 13:39:53.199 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 13:39:53.199 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 13:39:53.199 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 13:39:53.200 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 13:39:53.200 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 13:39:53.200 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 13:39:53.303 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 13:39:53.304 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 13:39:53.305 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 13:39:53.305 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 13:39:53.306 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 13:39:53.306 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 13:39:53.307 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 13:39:53.307 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 13:39:53.307 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 13:39:53.308 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 13:39:53.308 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 13:39:53.309 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 13:39:53.309 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 13:39:53.309 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 13:39:53.310 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 13:39:53.361 [Information] BackupService: Initializing backup service
2025-07-05 13:39:53.361 [Information] BackupService: Backup service initialized successfully
2025-07-05 13:39:53.413 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 13:39:53.413 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 13:39:53.416 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 13:39:53.417 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 13:39:53.472 [Information] BackupService: Getting predefined backup categories
2025-07-05 13:39:53.524 [Information] MainViewModel: Services initialized successfully
2025-07-05 13:39:53.529 [Information] MainViewModel: Scanning for Vocom devices
2025-07-05 13:39:53.531 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 13:39:53.532 [Information] VocomService: Using new enhanced device detection service
2025-07-05 13:39:53.532 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 13:39:53.532 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 13:39:54.020 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-05 13:39:54.021 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 13:39:54.021 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 13:39:54.022 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-05 13:39:54.022 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-05 13:39:54.023 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-05 13:39:54.023 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-05 13:39:54.024 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-05 13:39:54.555 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-05 13:39:54.556 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-05 13:39:54.556 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-05 13:39:54.557 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-05 13:39:54.557 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-05 13:39:54.558 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-05 13:39:54.558 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-05 13:39:54.559 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 13:39:54.591 [Debug] VocomService: Bluetooth is enabled
2025-07-05 13:39:54.592 [Debug] VocomService: Checking if WiFi is available
2025-07-05 13:39:54.594 [Debug] VocomService: WiFi is available
2025-07-05 13:39:54.595 [Information] VocomService: Found 3 Vocom devices
2025-07-05 13:39:54.598 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-05 13:39:59.967 [Debug] AppConfigurationService: Configuration value set for key 'Vocom.UseWiFiFallback'
2025-07-05 13:39:59.972 [Information] VocomService: Setting Vocom connection settings
2025-07-05 13:39:59.974 [Information] VocomService: Connection settings updated
2025-07-05 13:39:59.974 [Information] VocomService: Vocom connection settings applied successfully
2025-07-05 13:39:59.978 [Information] VocomService: Setting Vocom connection settings
2025-07-05 13:39:59.979 [Information] VocomService: Connection settings updated
2025-07-05 13:39:59.979 [Information] VocomService: Vocom connection settings applied successfully
2025-07-05 13:39:59.980 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 13:40:01.087 [Debug] AppConfigurationService: Configuration value set for key 'Vocom.UseWiFiFallback'
2025-07-05 13:40:01.088 [Information] VocomService: Setting Vocom connection settings
2025-07-05 13:40:01.088 [Information] VocomService: Connection settings updated
2025-07-05 13:40:01.088 [Information] VocomService: Vocom connection settings applied successfully
2025-07-05 13:40:01.091 [Information] VocomService: Setting Vocom connection settings
2025-07-05 13:40:01.091 [Information] VocomService: Connection settings updated
2025-07-05 13:40:01.092 [Information] VocomService: Vocom connection settings applied successfully
2025-07-05 13:40:01.092 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 13:40:09.361 [Information] MainViewModel: Connecting to Vocom device 88890300-DRIVER
2025-07-05 13:40:09.362 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-05 13:40:09.365 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-07-05 13:40:09.367 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-07-05 13:40:09.780 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-07-05 13:40:09.782 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-07-05 13:40:09.783 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-05 13:40:09.788 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-05 13:40:09.789 [Information] ECUCommunicationService: No ECUs are connected
2025-07-05 13:40:09.790 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-07-05 13:40:09.790 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-05 13:40:09.790 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-05 13:40:09.791 [Information] ECUCommunicationService: No ECUs are connected
2025-07-05 13:40:09.791 [Information] VocomService: Checking if PTT application is running
2025-07-05 13:40:09.810 [Information] VocomService: PTT application is not running
2025-07-05 13:40:09.811 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-05 13:40:09.811 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-05 13:40:09.811 [Information] VocomService: Checking if PTT application is running
2025-07-05 13:40:09.830 [Information] VocomService: PTT application is not running
2025-07-05 13:40:09.831 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-05 13:40:09.831 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-05 13:40:09.831 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2
2025-07-05 13:40:09.832 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-05 13:40:09.832 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-05 13:40:09.832 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-05 13:40:09.833 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-05 13:40:09.833 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-05 13:40:09.833 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-05 13:40:09.834 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-05 13:40:09.834 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-05 13:40:09.834 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-05 13:40:09.835 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 13:40:09.835 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 13:40:09.836 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 13:40:09.836 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 13:40:09.837 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 13:40:09.837 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 13:40:09.838 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 13:40:09.838 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 13:40:09.838 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 13:40:09.839 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 13:40:09.839 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 13:40:09.839 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 13:40:09.841 [Error] MainViewModel: Failed to connect to Vocom device 88890300-DRIVER
2025-07-05 13:40:15.862 [Information] MainViewModel: Connecting to Vocom device 88890300-BT
2025-07-05 13:40:15.863 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-05 13:40:15.863 [Information] VocomService: Checking if PTT application is running
2025-07-05 13:40:15.880 [Information] VocomService: PTT application is not running
2025-07-05 13:40:15.880 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-05 13:40:15.880 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 13:40:15.881 [Debug] VocomService: Bluetooth is enabled
2025-07-05 13:40:15.881 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-05 13:40:16.694 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-05 13:40:16.695 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-05 13:40:16.695 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-05 13:40:16.695 [Information] MainViewModel: Vocom device 88890300-BT connected
2025-07-05 13:40:16.696 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-05 13:40:16.696 [Information] MainViewModel: Connected to Vocom device 88890300-BT
2025-07-05 13:40:16.699 [Information] MainViewModel: Scanning for ECUs
2025-07-05 13:40:16.705 [Information] ECUCommunicationService: Scanning for ECUs
2025-07-05 13:40:16.705 [Information] ECUCommunicationService: Scanning for ECUs on the CAN bus...
2025-07-05 13:40:17.220 [Information] ECUCommunicationService: Scanning for ECUs using SPI protocol...
2025-07-05 13:40:17.534 [Information] ECUCommunicationService: Scanning for ECUs using SCI protocol...
2025-07-05 13:40:17.849 [Information] ECUCommunicationService: Scanning for ECUs using IIC protocol...
2025-07-05 13:40:18.166 [Information] ECUCommunicationService: Found 10 ECUs
2025-07-05 13:40:18.168 [Information] MainViewModel: Found 10 ECU(s)
2025-07-05 13:40:53.385 [Information] MainViewModel: Connecting to ECU DCM
2025-07-05 13:40:53.398 [Information] ECUCommunicationService: Connecting to ECU DCM
2025-07-05 13:40:53.398 [Information] ECUCommunicationService: Using high-speed communication for ECU DCM
2025-07-05 13:40:53.401 [Information] ECUCommunicationService: Setting operating mode to Bench for ECU DCM
2025-07-05 13:40:53.401 [Information] ECUCommunicationService: Attempting to connect to ECU DCM using SPI protocol with high-speed communication
2025-07-05 13:40:53.403 [Information] SPIProtocolHandler: Connecting to ECU DCM via SPI
2025-07-05 13:40:53.731 [Information] SPIProtocolHandler: Connected to ECU DCM via SPI
2025-07-05 13:40:53.733 [Information] ECUCommunicationService: Configuring MC9S12XEP100 specific settings for ECU DCM
2025-07-05 13:40:53.737 [Information] ECUCommunicationService: Configuring MC9S12XEP100 specific settings for ECU DCM
2025-07-05 13:40:53.737 [Information] ECUCommunicationService: Using MC9S12XEP100Integration for configuring ECU DCM
2025-07-05 13:40:53.739 [Information] MC9S12XEP100Helper: Configured ECU DCM with MC9S12XEP100-specific settings
2025-07-05 13:40:53.741 [Information] ECUCommunicationService: Configuring MC9S12XEP100 SPI settings for ECU DCM
2025-07-05 13:40:53.903 [Information] ECUCommunicationService: MC9S12XEP100 SPI settings configured for ECU DCM
2025-07-05 13:40:53.913 [Information] ECUCommunicationService: Getting MC9S12XEP100 flash memory configuration for ECU DCM
2025-07-05 13:40:53.918 [Information] VocomService: Reading MC9S12XEP100 specific registers from ECU 01474a9e-82a3-466f-afe8-709e3ae611e3
2025-07-05 13:40:54.142 [Information] VocomService: Read 20 MC9S12XEP100 specific registers from ECU 01474a9e-82a3-466f-afe8-709e3ae611e3
2025-07-05 13:40:54.144 [Information] ECUCommunicationService: Got MC9S12XEP100 flash memory configuration with 35 parameters
2025-07-05 13:40:54.149 [Information] SPIProtocolHandler: Setting operating mode to Bench via SPI
2025-07-05 13:40:54.150 [Information] SPIProtocolHandler: Configuring SPI controller for Bench mode
2025-07-05 13:40:54.150 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 13:40:54.151 [Information] SPIProtocolHandler: Writing 0x43 to SPI Baud Rate Register (0x00FA)
2025-07-05 13:40:54.214 [Information] SPIProtocolHandler: Operating mode set to Bench via SPI
2025-07-05 13:40:54.214 [Information] ECUCommunicationService: Reading initial parameters and faults from ECU DCM
2025-07-05 13:40:54.218 [Information] SPIProtocolHandler: Reading parameters from ECU DCM via SPI
2025-07-05 13:40:54.741 [Information] SPIProtocolHandler: Read 3 parameters from ECU DCM via SPI
2025-07-05 13:40:54.744 [Information] SPIProtocolHandler: Reading active faults from ECU DCM via SPI
2025-07-05 13:40:55.068 [Information] SPIProtocolHandler: Read 0 active faults from ECU DCM via SPI
2025-07-05 13:40:55.071 [Information] SPIProtocolHandler: Reading inactive faults from ECU DCM via SPI
2025-07-05 13:40:55.389 [Information] SPIProtocolHandler: Read 0 inactive faults from ECU DCM via SPI
2025-07-05 13:40:55.391 [Information] MainViewModel: ECU DCM connected
2025-07-05 13:40:55.397 [Information] ECUCommunicationService: Connected to ECU DCM
2025-07-05 13:40:55.399 [Information] MainViewModel: Connected to ECU DCM
2025-07-05 13:41:14.440 [Information] MainViewModel: Disconnecting from Vocom device
2025-07-05 13:41:14.440 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-07-05 13:41:14.441 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-07-05 13:41:14.845 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-07-05 13:41:14.845 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-07-05 13:41:14.846 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-05 13:41:14.846 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-05 13:41:14.846 [Information] ECUCommunicationService: Attempting to disconnect from 1 ECUs
2025-07-05 13:41:14.851 [Information] ECUCommunicationService: Disconnecting from ECU DCM
2025-07-05 13:41:14.851 [Error] ECUCommunicationService: Vocom device is not connected
2025-07-05 13:41:14.852 [Error] MainViewModel: ECU error: Vocom device is not connected
2025-07-05 13:41:14.852 [Error] ECUCommunicationService: Failed to gracefully disconnect from ECU DCM
2025-07-05 13:41:14.852 [Warning] ECUCommunicationService: Forcing disconnection for 1 remaining ECUs
2025-07-05 13:41:14.853 [Information] MainViewModel: ECU DCM disconnected
2025-07-05 13:41:14.857 [Information] ECUCommunicationService: Forced disconnection from ECU DCM
2025-07-05 13:41:14.857 [Information] ECUCommunicationService: Disconnected from some ECUs. 0 ECUs remain connected.
2025-07-05 13:41:14.857 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-07-05 13:41:14.858 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-05 13:41:14.858 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-05 13:41:14.858 [Information] ECUCommunicationService: No ECUs are connected
2025-07-05 13:41:14.859 [Information] MainViewModel: Disconnected from Vocom device
