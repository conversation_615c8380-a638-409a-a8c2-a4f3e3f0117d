Log started at 7/5/2025 10:34:36 AM
2025-07-05 10:34:36.646 [Information] LoggingService: Logging service initialized
2025-07-05 10:34:36.675 [Information] App: Starting integrated application initialization
2025-07-05 10:34:36.677 [Information] DependencyManager: Dependency manager initialized for x86 architecture
2025-07-05 10:34:36.681 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 10:34:36.708 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 10:34:36.709 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 10:34:36.711 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 10:34:36.714 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 10:34:36.730 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 10:34:36.740 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 10:34:36.743 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 10:34:36.747 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 10:34:36.748 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 10:34:36.752 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 10:34:36.756 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 10:34:36.759 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 10:34:36.760 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 10:34:36.764 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 10:34:36.767 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 10:34:36.771 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 10:34:36.772 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 10:34:36.776 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 10:34:36.779 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 10:34:36.783 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 10:34:36.783 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 10:34:36.791 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 10:34:36.801 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 10:34:36.802 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 10:34:36.822 [Debug] VCRedistBundler: Successfully loaded and verified: msvcr120.dll
2025-07-05 10:34:36.823 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 10:34:36.837 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp120.dll
2025-07-05 10:34:36.838 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 10:34:36.838 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 10:34:36.941 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-05 10:34:36.942 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 10:34:37.020 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-05 10:34:37.028 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 10:34:37.029 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 10:34:37.029 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 10:34:37.044 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-05 10:34:37.045 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-05 10:34:37.045 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-05 10:34:37.083 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-05 10:34:37.085 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-05 10:34:37.088 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 10:34:37.091 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 10:34:37.108 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 10:34:37.117 [Information] LibraryExtractor: Copying system libraries
2025-07-05 10:34:37.137 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 10:34:37.147 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 10:35:01.490 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-05 10:36:04.952 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-05 10:37:21.121 [Information] LibraryExtractor: Verifying library extraction
2025-07-05 10:37:21.122 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-05 10:37:21.122 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-05 10:37:21.123 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-05 10:37:21.123 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-05 10:37:21.123 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-05 10:37:21.137 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-05 10:37:21.140 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-05 10:37:21.142 [Information] DependencyManager: Initializing dependency manager
2025-07-05 10:37:21.143 [Information] DependencyManager: Setting up library search paths
2025-07-05 10:37:21.146 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 10:37:21.146 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 10:37:21.147 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 10:37:21.147 [Information] DependencyManager: Updated PATH environment variable
2025-07-05 10:37:21.153 [Information] DependencyManager: Verifying required directories
2025-07-05 10:37:21.154 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 10:37:21.154 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 10:37:21.155 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-05 10:37:21.155 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 10:37:21.159 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-05 10:37:21.183 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\SysWOW64\msvcr120.dll (x86)
2025-07-05 10:37:21.185 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\SysWOW64\msvcp120.dll (x86)
2025-07-05 10:37:21.199 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-05 10:37:21.202 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\SysWOW64\msvcp140.dll (x86)
2025-07-05 10:37:21.203 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\SysWOW64\vcruntime140.dll (x86)
2025-07-05 10:37:21.203 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 10:37:21.204 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 10:37:21.206 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 10:37:21.210 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-05 10:37:21.211 [Debug] DependencyManager: Architecture mismatch: Library WUDFPuma.dll is x64, process is x86
2025-07-05 10:37:21.212 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll
2025-07-05 10:37:21.213 [Warning] DependencyManager: Failed to load Critical library WUDFPuma.dll: Error 193
2025-07-05 10:37:21.340 [Information] DependencyManager: ✓ Loaded Critical library: apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll (x86)
2025-07-05 10:37:21.351 [Information] DependencyManager: ✓ Loaded Critical library: apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll (x86)
2025-07-05 10:37:21.747 [Information] DependencyManager: ✓ Loaded Critical library: Volvo.ApciPlus.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll (x86)
2025-07-05 10:37:21.887 [Information] DependencyManager: ✓ Loaded Critical library: Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll (x86)
2025-07-05 10:37:21.890 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll (x86)
2025-07-05 10:37:21.890 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\SysWOW64\msvcr120.dll (x86)
2025-07-05 10:37:21.893 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\SysWOW64\msvcp120.dll (x86)
2025-07-05 10:37:21.896 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-05 10:37:21.897 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\SysWOW64\msvcp140.dll (x86)
2025-07-05 10:37:21.898 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\SysWOW64\vcruntime140.dll (x86)
2025-07-05 10:37:21.898 [Information] DependencyManager: Setting up environment variables
2025-07-05 10:37:21.899 [Information] DependencyManager: Environment variables configured
2025-07-05 10:37:21.905 [Information] DependencyManager: Verifying library loading status
2025-07-05 10:37:22.421 [Information] DependencyManager: Library loading verification: 9/11 (81.8%) critical libraries loaded
2025-07-05 10:37:22.421 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-05 10:37:22.430 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-05 10:37:22.432 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-05 10:37:22.437 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-05 10:37:22.444 [Information] IntegratedStartupService: Verifying system readiness
2025-07-05 10:37:22.445 [Information] IntegratedStartupService: System readiness verification passed
2025-07-05 10:37:22.445 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-05 10:37:22.451 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-05 10:37:22.451 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 10:37:22.451 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 10:37:22.452 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 10:37:22.452 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-05 10:37:22.452 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-05 10:37:22.453 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-05 10:37:22.453 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 10:37:22.453 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-05 10:37:22.453 [Information] App: Integrated startup completed successfully
2025-07-05 10:37:22.457 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-05 10:37:22.720 [Information] App: Initializing application services
2025-07-05 10:37:22.722 [Information] AppConfigurationService: Initializing configuration service
2025-07-05 10:37:22.723 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 10:37:22.814 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 10:37:22.816 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-05 10:37:22.818 [Information] App: Configuration service initialized successfully
2025-07-05 10:37:22.819 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-05 10:37:22.820 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-05 10:37:22.831 [Information] App: Environment variable exists: True, not 'false': False
2025-07-05 10:37:22.831 [Information] App: Final useDummyImplementations value: False
2025-07-05 10:37:22.832 [Information] App: Updating config to NOT use dummy implementations
2025-07-05 10:37:22.834 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-05 10:37:22.851 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 10:37:22.852 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-05 10:37:22.853 [Information] App: usePatchedImplementation flag is: True
2025-07-05 10:37:22.853 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-05 10:37:22.853 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-05 10:37:22.854 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-05 10:37:22.854 [Information] App: verboseLogging flag is: True
2025-07-05 10:37:22.861 [Information] App: Verifying real hardware requirements...
2025-07-05 10:37:22.862 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-05 10:37:22.863 [Information] App: ✓ Found critical library: apci.dll
2025-07-05 10:37:22.863 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-05 10:37:22.863 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-05 10:37:22.864 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:37:22.864 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-05 10:37:22.864 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-05 10:37:22.865 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-05 10:37:22.879 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-07-05 10:37:22.880 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-07-05 10:37:22.881 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-05 10:37:22.884 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-07-05 10:37:22.884 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoFlashWR.Communication.dll
2025-07-05 10:37:22.922 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-05 10:37:22.922 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\patched_factory_created.txt
2025-07-05 10:37:22.923 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-07-05 10:37:22.923 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-07-05 10:37:22.923 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-05 10:37:23.028 [Information] App: Creating Vocom service (attempt 1/3)
2025-07-05 10:37:23.040 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-05 10:37:23.040 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-05 10:37:23.041 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-05 10:37:23.041 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-05 10:37:23.042 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-05 10:37:23.046 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-05 10:37:23.063 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-05 10:37:23.070 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-05 10:37:23.072 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-05 10:37:23.072 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-05 10:37:23.104 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-05 10:37:23.124 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-05 10:37:23.132 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-05 10:37:23.132 [Information] PhoenixVocomAdapter: Current process architecture: x86
2025-07-05 10:37:23.232 [Information] PhoenixVocomAdapter: Found compatible APCI library at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 10:37:23.235 [Information] PhoenixVocomAdapter: Successfully loaded APCI library from: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 10:37:23.238 [Warning] PhoenixVocomAdapter: No APCI initialize function found in library
2025-07-05 10:37:23.239 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-05 10:37:23.240 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-05 10:37:23.247 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-05 10:37:23.247 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-05 10:37:23.248 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-05 10:37:23.248 [Information] VocomDiagnosticTool: Successfully loaded apci.dll
2025-07-05 10:37:23.248 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_Initialize
2025-07-05 10:37:23.249 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_Shutdown
2025-07-05 10:37:23.249 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_DetectDevices
2025-07-05 10:37:23.249 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_ConnectDevice
2025-07-05 10:37:23.249 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_DisconnectDevice
2025-07-05 10:37:23.250 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_SendData
2025-07-05 10:37:23.250 [Warning] VocomDiagnosticTool: ✗ Missing function: ApciInitialize
2025-07-05 10:37:23.250 [Warning] VocomDiagnosticTool: ✗ Missing function: ApciShutdown
2025-07-05 10:37:23.258 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-05 10:37:23.258 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:37:23.259 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-05 10:37:23.260 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-05 10:37:23.266 [Error] VocomDiagnosticTool: Failed to load WUDFPuma.dll. Error: 0 (0x0)
2025-07-05 10:37:23.266 [Error] VocomDiagnosticTool: Unknown error code: 0
2025-07-05 10:37:23.267 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-05 10:37:23.268 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-05 10:37:23.268 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-05 10:37:23.270 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-05 10:37:23.271 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-05 10:37:23.271 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-05 10:37:23.271 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 10:37:23.272 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-05 10:37:23.273 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-05 10:37:23.273 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-05 10:37:23.277 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-05 10:37:23.281 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-05 10:37:23.281 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-05 10:37:23.300 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-05 10:37:23.301 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-05 10:37:23.301 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 10:37:23.302 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 10:37:23.314 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-05 10:37:23.316 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 10:37:23.319 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 10:37:23.321 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Rpci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 10:37:23.322 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Pc2.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 10:37:23.454 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 10:37:23.455 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 10:37:23.456 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 10:37:23.456 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 10:37:23.457 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 10:37:23.458 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 10:37:23.459 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixESW.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 10:37:23.461 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 10:37:23.462 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 10:37:23.463 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 10:37:23.725 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 10:37:23.725 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 10:37:23.729 [Information] VocomNativeInterop_Patch: Loading function pointers from Vocom driver DLL
2025-07-05 10:37:23.730 [Error] VocomNativeInterop_Patch: Failed to find any initialize function in the DLL
2025-07-05 10:37:23.731 [Error] VocomNativeInterop_Patch: Failed to load function pointers from Vocom driver DLL
2025-07-05 10:37:23.732 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-05 10:37:23.733 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-05 10:37:23.735 [Information] VocomDriver: Initializing Vocom driver
2025-07-05 10:37:23.737 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-05 10:37:23.752 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-05 10:37:23.753 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:37:23.753 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:37:23.754 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:37:23.755 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-05 10:37:23.757 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-05 10:37:23.758 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-05 10:37:23.761 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-05 10:37:23.762 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-05 10:37:23.763 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-05 10:37:23.763 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 10:37:23.909 [Warning] WUDFPumaDependencyResolver: Could not load dependency: WdfCoInstaller01009.dll
2025-07-05 10:37:24.229 [Warning] WUDFPumaDependencyResolver: Could not load dependency: WUDFUpdate_01009.dll
2025-07-05 10:37:24.537 [Warning] WUDFPumaDependencyResolver: Could not load dependency: winusbcoinstaller2.dll
2025-07-05 10:37:24.538 [Error] WUDFPumaDependencyResolver: Failed to load WUDFPuma.dll. Error code: 0 (0x0)
2025-07-05 10:37:24.539 [Error] WUDFPumaDependencyResolver: Error details: Unknown error code: 0
2025-07-05 10:37:24.539 [Warning] VocomNativeInterop: Failed to load WUDFPuma.dll or function pointers, falling back to modern .NET 8.0 implementation
2025-07-05 10:37:24.544 [Information] VocomNativeInterop: Using modern .NET 8.0 implementation for Vocom communication
2025-07-05 10:37:24.561 [Information] VocomNativeInterop: Initializing HID device detection for Vocom adapters
2025-07-05 10:37:24.604 [Information] VocomNativeInterop: No HID Vocom devices found
2025-07-05 10:37:24.609 [Information] VocomNativeInterop: Initializing serial port detection for Vocom adapters
2025-07-05 10:37:24.611 [Information] VocomNativeInterop: No serial ports found
2025-07-05 10:37:24.611 [Warning] VocomNativeInterop: Failed to initialize modern Vocom communication, no devices found
2025-07-05 10:37:24.613 [Error] VocomNativeInterop: Failed to initialize Vocom driver
2025-07-05 10:37:24.616 [Error] VocomDriver: Failed to initialize Vocom native interop
2025-07-05 10:37:24.616 [Warning] PatchedVocomServiceFactory: Failed to initialize standard Vocom driver, falling back to device driver
2025-07-05 10:37:24.623 [Information] VocomDeviceDriver: Initializing Vocom device driver
2025-07-05 10:37:24.624 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-05 10:37:24.625 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-05 10:37:24.628 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:37:24.628 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:37:24.628 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:37:24.628 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-05 10:37:24.629 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-05 10:37:24.630 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-05 10:37:24.632 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-05 10:37:24.633 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-05 10:37:24.634 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-05 10:37:24.634 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 10:37:24.641 [Warning] WUDFPumaDependencyResolver: Could not load dependency: WdfCoInstaller01009.dll
2025-07-05 10:37:24.649 [Warning] WUDFPumaDependencyResolver: Could not load dependency: WUDFUpdate_01009.dll
2025-07-05 10:37:24.654 [Warning] WUDFPumaDependencyResolver: Could not load dependency: winusbcoinstaller2.dll
2025-07-05 10:37:24.655 [Error] WUDFPumaDependencyResolver: Failed to load WUDFPuma.dll. Error code: 0 (0x0)
2025-07-05 10:37:24.655 [Error] WUDFPumaDependencyResolver: Error details: Unknown error code: 0
2025-07-05 10:37:24.656 [Warning] VocomNativeInterop: Failed to load WUDFPuma.dll or function pointers, falling back to modern .NET 8.0 implementation
2025-07-05 10:37:24.657 [Information] VocomNativeInterop: Using modern .NET 8.0 implementation for Vocom communication
2025-07-05 10:37:24.658 [Information] VocomNativeInterop: Initializing HID device detection for Vocom adapters
2025-07-05 10:37:24.658 [Information] VocomNativeInterop: No HID Vocom devices found
2025-07-05 10:37:24.659 [Information] VocomNativeInterop: Initializing serial port detection for Vocom adapters
2025-07-05 10:37:24.659 [Information] VocomNativeInterop: No serial ports found
2025-07-05 10:37:24.660 [Warning] VocomNativeInterop: Failed to initialize modern Vocom communication, no devices found
2025-07-05 10:37:24.664 [Error] VocomNativeInterop: Failed to initialize Vocom driver
2025-07-05 10:37:24.665 [Error] VocomDeviceDriver: Failed to initialize Vocom native interop layer
2025-07-05 10:37:24.666 [Error] PatchedVocomServiceFactory: Failed to initialize Vocom device driver, falling back to dummy implementation
2025-07-05 10:37:24.674 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 10:37:24.674 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 10:37:24.675 [Information] App: Initializing Vocom service
2025-07-05 10:37:24.675 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 10:37:24.675 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 10:37:24.678 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-05 10:37:24.779 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-05 10:37:24.779 [Information] App: Found 1 Vocom devices, attempting to connect to the first one
2025-07-05 10:37:24.782 [Information] DummyVocomService: Connecting to Vocom device Dummy Vocom Device (dummy)
2025-07-05 10:37:24.983 [Information] DummyVocomService: Connected to Vocom device Dummy Vocom Device (dummy)
2025-07-05 10:37:24.983 [Information] App: Connected to Vocom device Dummy Vocom Device
2025-07-05 10:37:24.997 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-05 10:37:25.000 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:37:25.001 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-05 10:37:25.005 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 10:37:25.007 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 10:37:25.007 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 10:37:25.014 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 10:37:25.021 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 10:37:25.025 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 10:37:25.040 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 10:37:25.045 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 10:37:25.063 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 10:37:25.066 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 10:37:25.078 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 10:37:25.079 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 10:37:25.080 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 10:37:25.080 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 10:37:25.080 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 10:37:25.081 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 10:37:25.081 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 10:37:25.081 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 10:37:25.082 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 10:37:25.083 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 10:37:25.084 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 10:37:25.084 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 10:37:25.084 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 10:37:25.084 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 10:37:25.085 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 10:37:25.085 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 10:37:25.085 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 10:37:25.089 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 10:37:25.095 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 10:37:25.096 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 10:37:25.116 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 10:37:25.118 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:37:25.124 [Information] CANRegisterAccess: Read value 0x20 from register 0x0141 (simulated)
2025-07-05 10:37:25.131 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:37:25.138 [Information] CANRegisterAccess: Read value 0x9C from register 0x0141 (simulated)
2025-07-05 10:37:25.144 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:37:25.150 [Information] CANRegisterAccess: Read value 0xF6 from register 0x0141 (simulated)
2025-07-05 10:37:25.156 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:37:25.161 [Information] CANRegisterAccess: Read value 0x16 from register 0x0141 (simulated)
2025-07-05 10:37:25.167 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:37:25.173 [Information] CANRegisterAccess: Read value 0xD6 from register 0x0141 (simulated)
2025-07-05 10:37:25.179 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:37:25.185 [Information] CANRegisterAccess: Read value 0xF3 from register 0x0141 (simulated)
2025-07-05 10:37:25.185 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 10:37:25.186 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 10:37:25.187 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 10:37:25.193 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 10:37:25.194 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 10:37:25.201 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 10:37:25.201 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 10:37:25.202 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 10:37:25.207 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 10:37:25.207 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 10:37:25.208 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 10:37:25.214 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 10:37:25.214 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 10:37:25.220 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 10:37:25.220 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 10:37:25.226 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 10:37:25.226 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 10:37:25.232 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 10:37:25.232 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 10:37:25.238 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 10:37:25.238 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 10:37:25.244 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 10:37:25.245 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 10:37:25.251 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 10:37:25.251 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 10:37:25.257 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 10:37:25.257 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 10:37:25.263 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 10:37:25.265 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 10:37:25.271 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 10:37:25.271 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 10:37:25.277 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 10:37:25.278 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 10:37:25.284 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 10:37:25.284 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 10:37:25.290 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 10:37:25.290 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 10:37:25.296 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 10:37:25.296 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 10:37:25.302 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 10:37:25.302 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 10:37:25.308 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 10:37:25.308 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 10:37:25.314 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 10:37:25.314 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 10:37:25.315 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 10:37:25.321 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 10:37:25.322 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 10:37:25.328 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 10:37:25.329 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:37:25.335 [Information] CANRegisterAccess: Read value 0xBC from register 0x0141 (simulated)
2025-07-05 10:37:25.336 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 10:37:25.336 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 10:37:25.336 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 10:37:25.337 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 10:37:25.344 [Information] CANRegisterAccess: Read value 0x9C from register 0x0140 (simulated)
2025-07-05 10:37:25.346 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 10:37:25.346 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 10:37:25.349 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 10:37:25.349 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 10:37:25.361 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 10:37:25.362 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 10:37:25.362 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 10:37:25.367 [Information] DummyVocomService: Sending data and waiting for response (dummy)
2025-07-05 10:37:25.519 [Information] DummyVocomService: Sent 4 bytes and received 6 bytes response (dummy)
2025-07-05 10:37:25.521 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 10:37:25.522 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 10:37:25.524 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 10:37:25.524 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 10:37:25.536 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 10:37:25.537 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 10:37:25.537 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 10:37:25.549 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 10:37:25.560 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 10:37:25.571 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 10:37:25.582 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 10:37:25.593 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 10:37:25.596 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 10:37:25.596 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 10:37:25.607 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 10:37:25.608 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 10:37:25.612 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 10:37:25.623 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 10:37:25.635 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 10:37:25.646 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 10:37:25.660 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 10:37:25.675 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 10:37:25.687 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 10:37:25.695 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 10:37:25.695 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 10:37:25.707 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 10:37:25.708 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 10:37:25.708 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 10:37:25.709 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 10:37:25.709 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 10:37:25.709 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 10:37:25.710 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 10:37:25.710 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 10:37:25.710 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 10:37:25.710 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 10:37:25.711 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 10:37:25.712 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 10:37:25.712 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 10:37:25.713 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 10:37:25.713 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 10:37:25.713 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 10:37:25.714 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 10:37:25.814 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 10:37:25.815 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 10:37:25.819 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 10:37:25.820 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:37:25.821 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 10:37:25.821 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 10:37:25.822 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:37:25.823 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 10:37:25.823 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 10:37:25.824 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:37:25.824 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 10:37:25.824 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 10:37:25.825 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:37:25.825 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 10:37:25.825 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 10:37:25.827 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-05 10:37:25.828 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-05 10:37:25.829 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-05 10:37:25.847 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-05 10:37:25.849 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-05 10:37:25.854 [Information] BackupService: Initializing backup service
2025-07-05 10:37:25.854 [Information] BackupService: Backup service initialized successfully
2025-07-05 10:37:25.854 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-05 10:37:25.855 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-05 10:37:25.857 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-05 10:37:25.913 [Information] BackupService: Compressing backup data
2025-07-05 10:37:25.929 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-07-05 10:37:25.934 [Information] BackupServiceFactory: Created template for category: Production
2025-07-05 10:37:25.936 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-05 10:37:25.937 [Information] BackupService: Compressing backup data
2025-07-05 10:37:25.938 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-07-05 10:37:25.939 [Information] BackupServiceFactory: Created template for category: Development
2025-07-05 10:37:25.939 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-05 10:37:25.940 [Information] BackupService: Compressing backup data
2025-07-05 10:37:25.942 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (449 bytes)
2025-07-05 10:37:25.942 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-05 10:37:25.943 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-05 10:37:25.943 [Information] BackupService: Compressing backup data
2025-07-05 10:37:25.946 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-07-05 10:37:25.947 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-05 10:37:25.947 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-05 10:37:25.948 [Information] BackupService: Compressing backup data
2025-07-05 10:37:25.952 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (447 bytes)
2025-07-05 10:37:25.952 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-05 10:37:25.953 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-05 10:37:25.953 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-05 10:37:25.953 [Information] BackupService: Compressing backup data
2025-07-05 10:37:25.954 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-07-05 10:37:25.955 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-05 10:37:25.955 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-05 10:37:25.957 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-05 10:37:25.961 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 10:37:25.974 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 10:37:26.090 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 10:37:26.091 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 10:37:26.094 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-05 10:37:26.095 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-05 10:37:26.095 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-05 10:37:26.097 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-05 10:37:26.098 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-05 10:37:26.107 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-05 10:37:26.108 [Information] App: Flash operation monitor service initialized successfully
2025-07-05 10:37:26.131 [Information] LicensingService: Initializing licensing service
2025-07-05 10:37:26.300 [Information] LicensingService: License information loaded successfully
2025-07-05 10:37:26.303 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-05 10:37:26.303 [Information] App: Licensing service initialized successfully
2025-07-05 10:37:26.304 [Information] App: License status: Trial
2025-07-05 10:37:26.304 [Information] App: Trial period: 30 days remaining
2025-07-05 10:37:26.305 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-05 10:37:26.347 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-05 10:37:26.523 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 10:37:26.524 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 10:37:26.574 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 10:37:26.574 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 10:37:26.575 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 10:37:26.575 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 10:37:26.575 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 10:37:26.579 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 10:37:26.579 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 10:37:26.582 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 10:37:26.582 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 10:37:26.583 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 10:37:26.595 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 10:37:26.595 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 10:37:26.596 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 10:37:26.596 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 10:37:26.596 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 10:37:26.596 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 10:37:26.597 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 10:37:26.597 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 10:37:26.597 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 10:37:26.598 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 10:37:26.598 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 10:37:26.598 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 10:37:26.599 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 10:37:26.599 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 10:37:26.599 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 10:37:26.599 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 10:37:26.600 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 10:37:26.600 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 10:37:26.608 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 10:37:26.608 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 10:37:26.609 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 10:37:26.609 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:37:26.615 [Information] CANRegisterAccess: Read value 0x57 from register 0x0141 (simulated)
2025-07-05 10:37:26.615 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 10:37:26.616 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 10:37:26.616 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 10:37:26.622 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 10:37:26.623 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 10:37:26.629 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 10:37:26.630 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 10:37:26.631 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 10:37:26.638 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 10:37:26.638 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 10:37:26.639 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 10:37:26.645 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 10:37:26.646 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 10:37:26.651 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 10:37:26.651 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 10:37:26.658 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 10:37:26.658 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 10:37:26.664 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 10:37:26.664 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 10:37:26.671 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 10:37:26.706 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 10:37:26.711 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 10:37:26.712 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 10:37:26.718 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 10:37:26.719 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 10:37:26.725 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 10:37:26.726 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 10:37:26.735 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 10:37:26.736 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 10:37:26.742 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 10:37:26.743 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 10:37:26.750 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 10:37:26.751 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 10:37:26.757 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 10:37:26.758 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 10:37:26.764 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 10:37:26.765 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 10:37:26.771 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 10:37:26.772 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 10:37:26.780 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 10:37:26.780 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 10:37:26.786 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 10:37:26.787 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 10:37:26.793 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 10:37:26.794 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 10:37:26.794 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 10:37:26.800 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 10:37:26.801 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 10:37:26.801 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 10:37:26.802 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:37:26.807 [Information] CANRegisterAccess: Read value 0x4F from register 0x0141 (simulated)
2025-07-05 10:37:26.812 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:37:26.818 [Information] CANRegisterAccess: Read value 0x54 from register 0x0141 (simulated)
2025-07-05 10:37:26.818 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 10:37:26.819 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 10:37:26.819 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 10:37:26.819 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 10:37:26.826 [Information] CANRegisterAccess: Read value 0x4A from register 0x0140 (simulated)
2025-07-05 10:37:26.832 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 10:37:26.838 [Information] CANRegisterAccess: Read value 0xDD from register 0x0140 (simulated)
2025-07-05 10:37:26.838 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 10:37:26.839 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 10:37:26.839 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 10:37:26.839 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 10:37:26.850 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 10:37:26.850 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 10:37:26.850 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 10:37:26.851 [Information] DummyVocomService: Sending data and waiting for response (dummy)
2025-07-05 10:37:27.001 [Information] DummyVocomService: Sent 4 bytes and received 6 bytes response (dummy)
2025-07-05 10:37:27.001 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 10:37:27.002 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 10:37:27.002 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 10:37:27.002 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 10:37:27.014 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 10:37:27.015 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 10:37:27.016 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 10:37:27.026 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 10:37:27.037 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 10:37:27.049 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 10:37:27.060 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 10:37:27.071 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 10:37:27.072 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 10:37:27.072 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 10:37:27.086 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 10:37:27.086 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 10:37:27.087 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 10:37:27.104 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 10:37:27.116 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 10:37:27.130 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 10:37:27.143 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 10:37:27.156 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 10:37:27.168 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 10:37:27.169 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 10:37:27.169 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 10:37:27.184 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 10:37:27.184 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 10:37:27.185 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 10:37:27.185 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 10:37:27.185 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 10:37:27.186 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 10:37:27.186 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 10:37:27.186 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 10:37:27.186 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 10:37:27.187 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 10:37:27.187 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 10:37:27.187 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 10:37:27.187 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 10:37:27.188 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 10:37:27.188 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 10:37:27.188 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 10:37:27.188 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 10:37:27.288 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 10:37:27.288 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 10:37:27.289 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 10:37:27.289 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:37:27.289 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 10:37:27.290 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 10:37:27.290 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:37:27.290 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 10:37:27.291 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 10:37:27.291 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:37:27.291 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 10:37:27.292 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 10:37:27.292 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:37:27.292 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 10:37:27.292 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 10:37:27.345 [Information] BackupService: Initializing backup service
2025-07-05 10:37:27.346 [Information] BackupService: Backup service initialized successfully
2025-07-05 10:37:27.397 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 10:37:27.397 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 10:37:27.400 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 10:37:27.400 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 10:37:27.452 [Information] BackupService: Getting predefined backup categories
2025-07-05 10:37:27.504 [Information] MainViewModel: Services initialized successfully
2025-07-05 10:37:27.514 [Information] MainViewModel: Scanning for Vocom devices
2025-07-05 10:37:27.515 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-05 10:37:27.615 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-05 10:37:27.616 [Information] MainViewModel: Found 1 Vocom device(s)
