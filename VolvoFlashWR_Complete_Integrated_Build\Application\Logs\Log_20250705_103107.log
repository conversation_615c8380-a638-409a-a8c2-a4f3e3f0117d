Log started at 7/5/2025 10:31:07 AM
2025-07-05 10:31:07.576 [Information] LoggingService: Logging service initialized
2025-07-05 10:31:07.600 [Information] App: Starting integrated application initialization
2025-07-05 10:31:07.602 [Information] DependencyManager: Dependency manager initialized for x86 architecture
2025-07-05 10:31:07.605 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 10:31:07.629 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 10:31:07.630 [Information] IntegratedStartupService: Created directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Libraries
2025-07-05 10:31:07.631 [Information] IntegratedStartupService: Created directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Config
2025-07-05 10:31:07.632 [Information] IntegratedStartupService: Created directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Backups
2025-07-05 10:31:07.632 [Information] IntegratedStartupService: Created directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Temp
2025-07-05 10:31:07.633 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 10:31:07.634 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 10:31:07.636 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 10:31:07.637 [Information] VCRedistBundler: Created VCRedist directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Libraries\VCRedist
2025-07-05 10:31:07.650 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 10:31:07.694 [Information] VCRedistBundler: Copied msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-05 10:31:07.706 [Information] VCRedistBundler: Copied msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-05 10:31:07.726 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 10:31:07.817 [Information] VCRedistBundler: Copied msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-05 10:31:07.822 [Information] VCRedistBundler: Copied vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-05 10:31:07.831 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 10:31:07.847 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 10:31:07.856 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 10:31:07.863 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 10:31:07.871 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 10:31:07.872 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 10:31:07.889 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 10:31:07.928 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 10:31:07.928 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 10:31:08.028 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 10:31:08.105 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 10:31:08.105 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 10:31:08.105 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 10:31:08.117 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-05 10:31:08.118 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-05 10:31:08.118 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-05 10:31:08.132 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-05 10:31:08.133 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-05 10:31:08.135 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 10:31:08.137 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 10:31:08.146 [Information] LibraryExtractor: Created directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Libraries\System
2025-07-05 10:31:08.146 [Information] LibraryExtractor: Created directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Vocom\Backup
2025-07-05 10:31:08.152 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 10:31:08.160 [Information] LibraryExtractor: Copying system libraries
2025-07-05 10:31:08.183 [Information] LibraryExtractor: Copied system library: WUDFPuma.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:31:08.189 [Information] LibraryExtractor: Copied system library: apci.dll from C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\apci.dll
2025-07-05 10:31:08.200 [Information] LibraryExtractor: Copied system library: Volvo.ApciPlus.dll from C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\Volvo.ApciPlus.dll
2025-07-05 10:31:08.281 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 10:31:08.408 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 10:31:40.402 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-05 10:32:25.279 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-05 10:33:41.555 [Information] LibraryExtractor: Verifying library extraction
2025-07-05 10:33:41.555 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-05 10:33:41.555 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-05 10:33:41.556 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-05 10:33:41.556 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-05 10:33:41.556 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-05 10:33:41.569 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-05 10:33:41.572 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-05 10:33:41.573 [Information] DependencyManager: Initializing dependency manager
2025-07-05 10:33:41.575 [Information] DependencyManager: Setting up library search paths
2025-07-05 10:33:41.576 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Libraries
2025-07-05 10:33:41.576 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Vocom
2025-07-05 10:33:41.577 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86
2025-07-05 10:33:41.577 [Information] DependencyManager: Updated PATH environment variable
2025-07-05 10:33:41.583 [Information] DependencyManager: Verifying required directories
2025-07-05 10:33:41.583 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Libraries
2025-07-05 10:33:41.583 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Vocom
2025-07-05 10:33:41.584 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Libraries\System
2025-07-05 10:33:41.584 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Config
2025-07-05 10:33:41.587 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-05 10:33:41.609 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\SysWOW64\msvcr120.dll (x86)
2025-07-05 10:33:41.611 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\SysWOW64\msvcp120.dll (x86)
2025-07-05 10:33:41.621 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-05 10:33:41.624 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\SysWOW64\msvcp140.dll (x86)
2025-07-05 10:33:41.626 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\SysWOW64\vcruntime140.dll (x86)
2025-07-05 10:33:41.628 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 10:33:41.630 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 10:33:41.635 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 10:33:41.638 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-05 10:33:41.642 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Libraries\WUDFPuma.dll
2025-07-05 10:33:41.644 [Warning] DependencyManager: Failed to load Critical library WUDFPuma.dll: Error 193
2025-07-05 10:33:41.774 [Information] DependencyManager: ✓ Loaded Critical library: apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Libraries\apci.dll (x86)
2025-07-05 10:33:41.775 [Warning] DependencyManager: Critical library not found: apcidb.dll
2025-07-05 10:33:42.124 [Information] DependencyManager: ✓ Loaded Critical library: Volvo.ApciPlus.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Libraries\Volvo.ApciPlus.dll (x86)
2025-07-05 10:33:42.125 [Warning] DependencyManager: Critical library not found: Volvo.ApciPlusData.dll
2025-07-05 10:33:42.126 [Warning] DependencyManager: Critical library not found: PhoenixGeneral.dll
2025-07-05 10:33:42.127 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\SysWOW64\msvcr120.dll (x86)
2025-07-05 10:33:42.127 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\SysWOW64\msvcp120.dll (x86)
2025-07-05 10:33:42.128 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-05 10:33:42.129 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\SysWOW64\msvcp140.dll (x86)
2025-07-05 10:33:42.130 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\SysWOW64\vcruntime140.dll (x86)
2025-07-05 10:33:42.130 [Information] DependencyManager: Setting up environment variables
2025-07-05 10:33:42.131 [Information] DependencyManager: Environment variables configured
2025-07-05 10:33:42.137 [Information] DependencyManager: Verifying library loading status
2025-07-05 10:33:42.661 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-05 10:33:42.661 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-05 10:33:42.662 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-05 10:33:42.670 [Information] IntegratedStartupService: Dependency status: 3 found, 8 missing
2025-07-05 10:33:42.671 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-05 10:33:42.710 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-05 10:33:42.715 [Information] IntegratedStartupService: Verifying system readiness
2025-07-05 10:33:42.716 [Information] IntegratedStartupService: System readiness verification passed
2025-07-05 10:33:42.716 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-05 10:33:42.722 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-05 10:33:42.722 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86
2025-07-05 10:33:42.722 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Libraries
2025-07-05 10:33:42.722 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Vocom
2025-07-05 10:33:42.723 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-05 10:33:42.723 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-05 10:33:42.723 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-05 10:33:42.723 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Libraries
2025-07-05 10:33:42.724 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-05 10:33:42.724 [Information] App: Integrated startup completed successfully
2025-07-05 10:33:42.728 [Information] App: System Status - Libraries: 3 available, Dependencies: 3 loaded
2025-07-05 10:33:42.979 [Information] App: Initializing application services
2025-07-05 10:33:42.984 [Information] AppConfigurationService: Initializing configuration service
2025-07-05 10:33:42.984 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Config
2025-07-05 10:33:42.984 [Information] AppConfigurationService: Configuration file not found, creating default
2025-07-05 10:33:42.987 [Warning] AppConfigurationService: Configuration service not initialized
2025-07-05 10:33:42.987 [Information] AppConfigurationService: Default configuration created
2025-07-05 10:33:42.988 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-05 10:33:42.988 [Information] App: Configuration service initialized successfully
2025-07-05 10:33:42.989 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-05 10:33:42.989 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-05 10:33:42.995 [Information] App: Environment variable exists: True, not 'false': False
2025-07-05 10:33:42.995 [Information] App: Final useDummyImplementations value: False
2025-07-05 10:33:42.996 [Information] App: Updating config to NOT use dummy implementations
2025-07-05 10:33:43.099 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Config\app_config.json
2025-07-05 10:33:43.102 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-05 10:33:43.102 [Information] App: usePatchedImplementation flag is: True
2025-07-05 10:33:43.103 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-05 10:33:43.103 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Libraries'
2025-07-05 10:33:43.104 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-05 10:33:43.104 [Information] App: verboseLogging flag is: True
2025-07-05 10:33:43.110 [Information] App: Verifying real hardware requirements...
2025-07-05 10:33:43.111 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-05 10:33:43.111 [Information] App: ✓ Found critical library: apci.dll
2025-07-05 10:33:43.111 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-05 10:33:43.112 [Warning] App: ✗ Missing critical library: Volvo.ApciPlusData.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Libraries\Volvo.ApciPlusData.dll
2025-07-05 10:33:43.112 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:33:43.112 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-05 10:33:43.113 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Vocom\config.json
2025-07-05 10:33:43.113 [Warning] App: ⚠ Some real hardware requirements are missing - application may fall back to dummy mode
2025-07-05 10:33:43.124 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-07-05 10:33:43.124 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-07-05 10:33:43.126 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-05 10:33:43.129 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-07-05 10:33:43.129 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\VolvoFlashWR.Communication.dll
2025-07-05 10:33:43.158 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-05 10:33:43.159 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\patched_factory_created.txt
2025-07-05 10:33:43.160 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-07-05 10:33:43.160 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-07-05 10:33:43.160 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-05 10:33:43.239 [Information] App: Creating Vocom service (attempt 1/3)
2025-07-05 10:33:43.242 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-05 10:33:43.242 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-05 10:33:43.242 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-05 10:33:43.243 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-05 10:33:43.243 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-05 10:33:43.246 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-05 10:33:43.261 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-05 10:33:43.272 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-05 10:33:43.273 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-05 10:33:43.274 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-05 10:33:43.299 [Information] PhoenixVocomAdapter: Copied apci.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\apci.dll
2025-07-05 10:33:43.302 [Information] PhoenixVocomAdapter: Copied apci.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\apci.dll
2025-07-05 10:33:43.305 [Information] PhoenixVocomAdapter: Copied apcidb.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\apcidb.dll
2025-07-05 10:33:43.308 [Information] PhoenixVocomAdapter: Copied apcidb.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\apcidb.dll
2025-07-05 10:33:43.319 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Volvo.ApciPlus.dll
2025-07-05 10:33:43.323 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Volvo.ApciPlus.dll
2025-07-05 10:33:43.328 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Volvo.ApciPlusData.dll
2025-07-05 10:33:43.330 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Volvo.ApciPlusData.dll
2025-07-05 10:33:43.335 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Volvo.ApciPlusTea2Data.dll
2025-07-05 10:33:43.337 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Volvo.ApciPlusTea2Data.dll
2025-07-05 10:33:43.342 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interface.dll
2025-07-05 10:33:43.344 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Volvo.NAMS.AC.Services.Interface.dll
2025-07-05 10:33:43.349 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-05 10:33:43.351 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-05 10:33:43.354 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Volvo.NVS.Core.dll
2025-07-05 10:33:43.356 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Volvo.NVS.Core.dll
2025-07-05 10:33:43.359 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Volvo.NVS.Logging.dll
2025-07-05 10:33:43.361 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Volvo.NVS.Logging.dll
2025-07-05 10:33:43.363 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Volvo.NVS.Persistence.dll
2025-07-05 10:33:43.366 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Volvo.NVS.Persistence.dll
2025-07-05 10:33:43.370 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Volvo.NVS.Persistence.NHibernate.dll
2025-07-05 10:33:43.371 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Volvo.NVS.Persistence.NHibernate.dll
2025-07-05 10:33:43.375 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\VolvoIt.Baf.Utility.dll
2025-07-05 10:33:43.377 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\VolvoIt.Baf.Utility.dll
2025-07-05 10:33:43.379 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-05 10:33:43.380 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-05 10:33:43.385 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\VolvoIt.Waf.ServiceContract.dll
2025-07-05 10:33:43.387 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\VolvoIt.Waf.ServiceContract.dll
2025-07-05 10:33:43.389 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\VolvoIt.Waf.Utility.dll
2025-07-05 10:33:43.391 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\VolvoIt.Waf.Utility.dll
2025-07-05 10:33:43.392 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Config\Volvo.ApciPlus.dll.config
2025-07-05 10:33:43.394 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Volvo.ApciPlus.dll.config
2025-07-05 10:33:43.395 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Config\Volvo.ApciPlusData.dll.config
2025-07-05 10:33:43.397 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Volvo.ApciPlusData.dll.config
2025-07-05 10:33:43.407 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Dependencies\NHibernate.dll
2025-07-05 10:33:43.411 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\NHibernate.dll
2025-07-05 10:33:43.414 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Dependencies\NHibernate.Caches.SysCache2.dll
2025-07-05 10:33:43.415 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\NHibernate.Caches.SysCache2.dll
2025-07-05 10:33:43.418 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Dependencies\Iesi.Collections.dll
2025-07-05 10:33:43.420 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Iesi.Collections.dll
2025-07-05 10:33:43.423 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Dependencies\Ionic.Zip.Reduced.dll
2025-07-05 10:33:43.425 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Ionic.Zip.Reduced.dll
2025-07-05 10:33:43.435 [Information] PhoenixVocomAdapter: Copied SharpCompress.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Dependencies\SharpCompress.dll
2025-07-05 10:33:43.440 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Dependencies\DotNetZip.dll
2025-07-05 10:33:43.442 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\DotNetZip.dll
2025-07-05 10:33:43.447 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Dependencies\ICSharpCode.SharpZipLib.dll
2025-07-05 10:33:43.449 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\ICSharpCode.SharpZipLib.dll
2025-07-05 10:33:43.452 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Vodia.CommonDomain.Model.dll
2025-07-05 10:33:43.454 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Vodia.CommonDomain.Model.dll
2025-07-05 10:33:43.457 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Vodia.Contracts.Common.dll
2025-07-05 10:33:43.459 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Vodia.Contracts.Common.dll
2025-07-05 10:33:43.462 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Vodia.UtilityComponent.dll
2025-07-05 10:33:43.463 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Vodia.UtilityComponent.dll
2025-07-05 10:33:43.468 [Information] PhoenixVocomAdapter: Copied log4net.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Dependencies\log4net.dll
2025-07-05 10:33:43.469 [Information] PhoenixVocomAdapter: Copied log4net.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\log4net.dll
2025-07-05 10:33:43.475 [Information] PhoenixVocomAdapter: Copied Newtonsoft.Json.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Dependencies\Newtonsoft.Json.dll
2025-07-05 10:33:43.479 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\Dependencies\AutoMapper.dll
2025-07-05 10:33:43.480 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\AutoMapper.dll
2025-07-05 10:33:43.482 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-05 10:33:43.484 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.AppContext.dll
2025-07-05 10:33:43.486 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.AppContext.dll
2025-07-05 10:33:43.488 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Buffers.dll
2025-07-05 10:33:43.489 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Buffers.dll
2025-07-05 10:33:43.491 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Collections.Concurrent.dll
2025-07-05 10:33:43.493 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Collections.Concurrent.dll
2025-07-05 10:33:43.495 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Collections.dll
2025-07-05 10:33:43.496 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Collections.dll
2025-07-05 10:33:43.502 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Collections.NonGeneric.dll
2025-07-05 10:33:43.503 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Collections.NonGeneric.dll
2025-07-05 10:33:43.506 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Collections.Specialized.dll
2025-07-05 10:33:43.507 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Collections.Specialized.dll
2025-07-05 10:33:43.509 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.ComponentModel.dll
2025-07-05 10:33:43.510 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.ComponentModel.dll
2025-07-05 10:33:43.513 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.ComponentModel.EventBasedAsync.dll
2025-07-05 10:33:43.515 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.ComponentModel.EventBasedAsync.dll
2025-07-05 10:33:43.518 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.ComponentModel.Primitives.dll
2025-07-05 10:33:43.519 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.ComponentModel.Primitives.dll
2025-07-05 10:33:43.521 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.ComponentModel.TypeConverter.dll
2025-07-05 10:33:43.522 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.ComponentModel.TypeConverter.dll
2025-07-05 10:33:43.524 [Information] PhoenixVocomAdapter: Copied System.Console.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Console.dll
2025-07-05 10:33:43.526 [Information] PhoenixVocomAdapter: Copied System.Console.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Console.dll
2025-07-05 10:33:43.528 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Data.Common.dll
2025-07-05 10:33:43.532 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Data.Common.dll
2025-07-05 10:33:43.535 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Data.SQLite.dll
2025-07-05 10:33:43.537 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Data.SQLite.dll
2025-07-05 10:33:43.541 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Data.SqlServerCe.dll
2025-07-05 10:33:43.542 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Data.SqlServerCe.dll
2025-07-05 10:33:43.544 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Diagnostics.Contracts.dll
2025-07-05 10:33:43.546 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Diagnostics.Contracts.dll
2025-07-05 10:33:43.547 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Diagnostics.Debug.dll
2025-07-05 10:33:43.549 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Diagnostics.Debug.dll
2025-07-05 10:33:43.551 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Diagnostics.FileVersionInfo.dll
2025-07-05 10:33:43.552 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Diagnostics.FileVersionInfo.dll
2025-07-05 10:33:43.554 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Diagnostics.Process.dll
2025-07-05 10:33:43.555 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Diagnostics.Process.dll
2025-07-05 10:33:43.557 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Diagnostics.StackTrace.dll
2025-07-05 10:33:43.558 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Diagnostics.StackTrace.dll
2025-07-05 10:33:43.560 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Diagnostics.TextWriterTraceListener.dll
2025-07-05 10:33:43.562 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Diagnostics.TextWriterTraceListener.dll
2025-07-05 10:33:43.564 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Diagnostics.Tools.dll
2025-07-05 10:33:43.565 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Diagnostics.Tools.dll
2025-07-05 10:33:43.567 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Diagnostics.TraceSource.dll
2025-07-05 10:33:43.569 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Diagnostics.TraceSource.dll
2025-07-05 10:33:43.571 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Diagnostics.Tracing.dll
2025-07-05 10:33:43.572 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Diagnostics.Tracing.dll
2025-07-05 10:33:43.574 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Drawing.Primitives.dll
2025-07-05 10:33:43.575 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Drawing.Primitives.dll
2025-07-05 10:33:43.577 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Dynamic.Runtime.dll
2025-07-05 10:33:43.578 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Dynamic.Runtime.dll
2025-07-05 10:33:43.581 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Globalization.Calendars.dll
2025-07-05 10:33:43.582 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Globalization.Calendars.dll
2025-07-05 10:33:43.584 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Globalization.dll
2025-07-05 10:33:43.585 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Globalization.dll
2025-07-05 10:33:43.588 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Globalization.Extensions.dll
2025-07-05 10:33:43.590 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Globalization.Extensions.dll
2025-07-05 10:33:43.595 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.IO.Compression.dll
2025-07-05 10:33:43.597 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.IO.Compression.dll
2025-07-05 10:33:43.603 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.IO.Compression.ZipFile.dll
2025-07-05 10:33:43.604 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.IO.Compression.ZipFile.dll
2025-07-05 10:33:43.606 [Information] PhoenixVocomAdapter: Copied System.IO.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.IO.dll
2025-07-05 10:33:43.608 [Information] PhoenixVocomAdapter: Copied System.IO.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.IO.dll
2025-07-05 10:33:43.610 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.IO.FileSystem.dll
2025-07-05 10:33:43.611 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.IO.FileSystem.dll
2025-07-05 10:33:43.613 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.IO.FileSystem.DriveInfo.dll
2025-07-05 10:33:43.615 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.IO.FileSystem.DriveInfo.dll
2025-07-05 10:33:43.619 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.IO.FileSystem.Primitives.dll
2025-07-05 10:33:43.621 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.IO.FileSystem.Primitives.dll
2025-07-05 10:33:43.623 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.IO.FileSystem.Watcher.dll
2025-07-05 10:33:43.624 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.IO.FileSystem.Watcher.dll
2025-07-05 10:33:43.626 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.IO.IsolatedStorage.dll
2025-07-05 10:33:43.628 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.IO.IsolatedStorage.dll
2025-07-05 10:33:43.630 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.IO.MemoryMappedFiles.dll
2025-07-05 10:33:43.632 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.IO.MemoryMappedFiles.dll
2025-07-05 10:33:43.636 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.IO.Pipes.dll
2025-07-05 10:33:43.638 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.IO.Pipes.dll
2025-07-05 10:33:43.640 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.IO.UnmanagedMemoryStream.dll
2025-07-05 10:33:43.642 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.IO.UnmanagedMemoryStream.dll
2025-07-05 10:33:43.644 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Linq.dll
2025-07-05 10:33:43.645 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Linq.dll
2025-07-05 10:33:43.647 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Linq.Expressions.dll
2025-07-05 10:33:43.650 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Linq.Expressions.dll
2025-07-05 10:33:43.654 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Linq.Parallel.dll
2025-07-05 10:33:43.655 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Linq.Parallel.dll
2025-07-05 10:33:43.657 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Linq.Queryable.dll
2025-07-05 10:33:43.659 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Linq.Queryable.dll
2025-07-05 10:33:43.665 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Memory.dll
2025-07-05 10:33:43.667 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Memory.dll
2025-07-05 10:33:43.670 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Net.Http.dll
2025-07-05 10:33:43.671 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Net.Http.dll
2025-07-05 10:33:43.673 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Net.NameResolution.dll
2025-07-05 10:33:43.674 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Net.NameResolution.dll
2025-07-05 10:33:43.676 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Net.NetworkInformation.dll
2025-07-05 10:33:43.678 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Net.NetworkInformation.dll
2025-07-05 10:33:43.680 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Net.Ping.dll
2025-07-05 10:33:43.681 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Net.Ping.dll
2025-07-05 10:33:43.683 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Net.Primitives.dll
2025-07-05 10:33:43.685 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Net.Primitives.dll
2025-07-05 10:33:43.687 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Net.Requests.dll
2025-07-05 10:33:43.688 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Net.Requests.dll
2025-07-05 10:33:43.690 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Net.Security.dll
2025-07-05 10:33:43.692 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Net.Security.dll
2025-07-05 10:33:43.695 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Net.Sockets.dll
2025-07-05 10:33:43.696 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Net.Sockets.dll
2025-07-05 10:33:43.703 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Net.WebHeaderCollection.dll
2025-07-05 10:33:43.705 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Net.WebHeaderCollection.dll
2025-07-05 10:33:43.707 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Net.WebSockets.Client.dll
2025-07-05 10:33:43.709 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Net.WebSockets.Client.dll
2025-07-05 10:33:43.711 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Net.WebSockets.dll
2025-07-05 10:33:43.713 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Net.WebSockets.dll
2025-07-05 10:33:43.722 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Numerics.Vectors.dll
2025-07-05 10:33:43.724 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Numerics.Vectors.dll
2025-07-05 10:33:43.726 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.ObjectModel.dll
2025-07-05 10:33:43.727 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.ObjectModel.dll
2025-07-05 10:33:43.729 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Reflection.dll
2025-07-05 10:33:43.731 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Reflection.dll
2025-07-05 10:33:43.735 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Reflection.Extensions.dll
2025-07-05 10:33:43.736 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Reflection.Extensions.dll
2025-07-05 10:33:43.738 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Reflection.Primitives.dll
2025-07-05 10:33:43.740 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Reflection.Primitives.dll
2025-07-05 10:33:43.742 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Resources.Reader.dll
2025-07-05 10:33:43.743 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Resources.Reader.dll
2025-07-05 10:33:43.745 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Resources.ResourceManager.dll
2025-07-05 10:33:43.746 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Resources.ResourceManager.dll
2025-07-05 10:33:43.748 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Resources.Writer.dll
2025-07-05 10:33:43.751 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Resources.Writer.dll
2025-07-05 10:33:43.753 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Runtime.CompilerServices.Unsafe.dll
2025-07-05 10:33:43.754 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Runtime.CompilerServices.Unsafe.dll
2025-07-05 10:33:43.756 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Runtime.CompilerServices.VisualC.dll
2025-07-05 10:33:43.757 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Runtime.CompilerServices.VisualC.dll
2025-07-05 10:33:43.760 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Runtime.dll
2025-07-05 10:33:43.761 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Runtime.dll
2025-07-05 10:33:43.763 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Runtime.Extensions.dll
2025-07-05 10:33:43.764 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Runtime.Extensions.dll
2025-07-05 10:33:43.767 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Runtime.Handles.dll
2025-07-05 10:33:43.769 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Runtime.Handles.dll
2025-07-05 10:33:43.771 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Runtime.InteropServices.dll
2025-07-05 10:33:43.773 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Runtime.InteropServices.dll
2025-07-05 10:33:43.775 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-05 10:33:43.776 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-05 10:33:43.778 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Runtime.Numerics.dll
2025-07-05 10:33:43.779 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Runtime.Numerics.dll
2025-07-05 10:33:43.781 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Runtime.Serialization.Formatters.dll
2025-07-05 10:33:43.783 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Runtime.Serialization.Formatters.dll
2025-07-05 10:33:43.786 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Runtime.Serialization.Json.dll
2025-07-05 10:33:43.787 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Runtime.Serialization.Json.dll
2025-07-05 10:33:43.789 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Runtime.Serialization.Primitives.dll
2025-07-05 10:33:43.791 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Runtime.Serialization.Primitives.dll
2025-07-05 10:33:43.792 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Runtime.Serialization.Xml.dll
2025-07-05 10:33:43.794 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Runtime.Serialization.Xml.dll
2025-07-05 10:33:43.795 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Security.Claims.dll
2025-07-05 10:33:43.797 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Security.Claims.dll
2025-07-05 10:33:43.799 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Security.Cryptography.Algorithms.dll
2025-07-05 10:33:43.801 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Security.Cryptography.Algorithms.dll
2025-07-05 10:33:43.803 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Security.Cryptography.Csp.dll
2025-07-05 10:33:43.804 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Security.Cryptography.Csp.dll
2025-07-05 10:33:43.806 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Security.Cryptography.Encoding.dll
2025-07-05 10:33:43.807 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Security.Cryptography.Encoding.dll
2025-07-05 10:33:43.810 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Security.Cryptography.Primitives.dll
2025-07-05 10:33:43.811 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Security.Cryptography.Primitives.dll
2025-07-05 10:33:43.813 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Security.Cryptography.X509Certificates.dll
2025-07-05 10:33:43.815 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Security.Cryptography.X509Certificates.dll
2025-07-05 10:33:43.818 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Security.Principal.dll
2025-07-05 10:33:43.819 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Security.Principal.dll
2025-07-05 10:33:43.821 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Security.SecureString.dll
2025-07-05 10:33:43.822 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Security.SecureString.dll
2025-07-05 10:33:43.824 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Text.Encoding.dll
2025-07-05 10:33:43.826 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Text.Encoding.dll
2025-07-05 10:33:43.828 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Text.Encoding.Extensions.dll
2025-07-05 10:33:43.829 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Text.Encoding.Extensions.dll
2025-07-05 10:33:43.831 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Text.RegularExpressions.dll
2025-07-05 10:33:43.834 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Text.RegularExpressions.dll
2025-07-05 10:33:43.836 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Threading.dll
2025-07-05 10:33:43.837 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Threading.dll
2025-07-05 10:33:43.840 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Threading.Overlapped.dll
2025-07-05 10:33:43.841 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Threading.Overlapped.dll
2025-07-05 10:33:43.843 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Threading.Tasks.dll
2025-07-05 10:33:43.845 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Threading.Tasks.dll
2025-07-05 10:33:43.853 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Threading.Tasks.Extensions.dll
2025-07-05 10:33:43.854 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Threading.Tasks.Extensions.dll
2025-07-05 10:33:43.856 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Threading.Tasks.Parallel.dll
2025-07-05 10:33:43.858 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Threading.Tasks.Parallel.dll
2025-07-05 10:33:43.860 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Threading.Thread.dll
2025-07-05 10:33:43.861 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Threading.Thread.dll
2025-07-05 10:33:43.863 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Threading.ThreadPool.dll
2025-07-05 10:33:43.864 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Threading.ThreadPool.dll
2025-07-05 10:33:43.868 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Threading.Timer.dll
2025-07-05 10:33:43.870 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Threading.Timer.dll
2025-07-05 10:33:43.872 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.ValueTuple.dll
2025-07-05 10:33:43.873 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.ValueTuple.dll
2025-07-05 10:33:43.876 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Xml.ReaderWriter.dll
2025-07-05 10:33:43.877 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Xml.ReaderWriter.dll
2025-07-05 10:33:43.879 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Xml.XDocument.dll
2025-07-05 10:33:43.881 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Xml.XDocument.dll
2025-07-05 10:33:43.884 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Xml.XmlDocument.dll
2025-07-05 10:33:43.886 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Xml.XmlDocument.dll
2025-07-05 10:33:43.888 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Xml.XmlSerializer.dll
2025-07-05 10:33:43.889 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Xml.XmlSerializer.dll
2025-07-05 10:33:43.892 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Xml.XPath.dll
2025-07-05 10:33:43.893 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Xml.XPath.dll
2025-07-05 10:33:43.896 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\System.Xml.XPath.XDocument.dll
2025-07-05 10:33:43.897 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\System.Xml.XPath.XDocument.dll
2025-07-05 10:33:43.900 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Drivers\Phoenix\System\SystemInterface.dll
2025-07-05 10:33:43.902 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\SystemInterface.dll
2025-07-05 10:33:43.902 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-05 10:33:43.909 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-05 10:33:43.910 [Information] PhoenixVocomAdapter: Current process architecture: x86
2025-07-05 10:33:44.085 [Information] PhoenixVocomAdapter: Found compatible APCI library at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\apci.dll
2025-07-05 10:33:44.097 [Information] PhoenixVocomAdapter: Successfully loaded APCI library from: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\apci.dll
2025-07-05 10:33:44.100 [Warning] PhoenixVocomAdapter: No APCI initialize function found in library
2025-07-05 10:33:44.101 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-05 10:33:44.102 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-05 10:33:44.112 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-05 10:33:44.112 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-05 10:33:44.113 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-05 10:33:44.113 [Information] VocomDiagnosticTool: Successfully loaded apci.dll
2025-07-05 10:33:44.113 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_Initialize
2025-07-05 10:33:44.114 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_Shutdown
2025-07-05 10:33:44.114 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_DetectDevices
2025-07-05 10:33:44.114 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_ConnectDevice
2025-07-05 10:33:44.114 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_DisconnectDevice
2025-07-05 10:33:44.115 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_SendData
2025-07-05 10:33:44.115 [Warning] VocomDiagnosticTool: ✗ Missing function: ApciInitialize
2025-07-05 10:33:44.116 [Warning] VocomDiagnosticTool: ✗ Missing function: ApciShutdown
2025-07-05 10:33:44.125 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-05 10:33:44.126 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:33:44.126 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-05 10:33:44.126 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-05 10:33:44.132 [Error] VocomDiagnosticTool: Failed to load WUDFPuma.dll. Error: 0 (0x0)
2025-07-05 10:33:44.133 [Error] VocomDiagnosticTool: Unknown error code: 0
2025-07-05 10:33:44.136 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-05 10:33:44.136 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-05 10:33:44.137 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-05 10:33:44.140 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-05 10:33:44.140 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-05 10:33:44.141 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-05 10:33:44.141 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 10:33:44.141 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-05 10:33:44.144 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-05 10:33:44.144 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-05 10:33:44.147 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-05 10:33:44.150 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-05 10:33:44.151 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-05 10:33:44.191 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-05 10:33:44.193 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-05 10:33:44.197 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\apci.dll
2025-07-05 10:33:44.198 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\apci.dll
2025-07-05 10:33:44.213 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-05 10:33:44.215 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\
2025-07-05 10:33:44.336 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\
2025-07-05 10:33:44.336 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-05 10:33:44.337 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-05 10:33:44.530 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\
2025-07-05 10:33:44.737 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\
2025-07-05 10:33:44.896 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\
2025-07-05 10:33:45.206 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\
2025-07-05 10:33:45.454 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\
2025-07-05 10:33:45.454 [Warning] VocomNativeInterop_Patch: Dependency PhoenixGeneral.dll not found in any search path
2025-07-05 10:33:45.454 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-05 10:33:45.656 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\
2025-07-05 10:33:45.958 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\
2025-07-05 10:33:46.207 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\
2025-07-05 10:33:46.460 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\
2025-07-05 10:33:46.560 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\
2025-07-05 10:33:46.563 [Information] VocomNativeInterop_Patch: Loading function pointers from Vocom driver DLL
2025-07-05 10:33:46.564 [Error] VocomNativeInterop_Patch: Failed to find any initialize function in the DLL
2025-07-05 10:33:46.565 [Error] VocomNativeInterop_Patch: Failed to load function pointers from Vocom driver DLL
2025-07-05 10:33:46.566 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-05 10:33:46.566 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-05 10:33:46.570 [Information] VocomDriver: Initializing Vocom driver
2025-07-05 10:33:46.572 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-05 10:33:46.593 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-05 10:33:46.593 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:33:46.594 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:33:46.595 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:33:46.595 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-05 10:33:46.599 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-05 10:33:46.600 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-05 10:33:46.603 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-05 10:33:46.603 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-05 10:33:46.604 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-05 10:33:46.604 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 10:33:46.610 [Warning] WUDFPumaDependencyResolver: Could not load dependency: WdfCoInstaller01009.dll
2025-07-05 10:33:46.615 [Warning] WUDFPumaDependencyResolver: Could not load dependency: WUDFUpdate_01009.dll
2025-07-05 10:33:46.620 [Warning] WUDFPumaDependencyResolver: Could not load dependency: winusbcoinstaller2.dll
2025-07-05 10:33:46.621 [Error] WUDFPumaDependencyResolver: Failed to load WUDFPuma.dll. Error code: 0 (0x0)
2025-07-05 10:33:46.621 [Error] WUDFPumaDependencyResolver: Error details: Unknown error code: 0
2025-07-05 10:33:46.622 [Warning] VocomNativeInterop: Failed to load WUDFPuma.dll or function pointers, falling back to modern .NET 8.0 implementation
2025-07-05 10:33:46.623 [Information] VocomNativeInterop: Using modern .NET 8.0 implementation for Vocom communication
2025-07-05 10:33:46.650 [Information] VocomNativeInterop: Initializing HID device detection for Vocom adapters
2025-07-05 10:33:46.723 [Information] VocomNativeInterop: No HID Vocom devices found
2025-07-05 10:33:46.728 [Information] VocomNativeInterop: Initializing serial port detection for Vocom adapters
2025-07-05 10:33:46.730 [Information] VocomNativeInterop: No serial ports found
2025-07-05 10:33:46.730 [Warning] VocomNativeInterop: Failed to initialize modern Vocom communication, no devices found
2025-07-05 10:33:46.731 [Error] VocomNativeInterop: Failed to initialize Vocom driver
2025-07-05 10:33:46.732 [Error] VocomDriver: Failed to initialize Vocom native interop
2025-07-05 10:33:46.733 [Warning] PatchedVocomServiceFactory: Failed to initialize standard Vocom driver, falling back to device driver
2025-07-05 10:33:46.735 [Information] VocomDeviceDriver: Initializing Vocom device driver
2025-07-05 10:33:46.735 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-05 10:33:46.735 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-05 10:33:46.736 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:33:46.736 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:33:46.736 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 10:33:46.737 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-05 10:33:46.737 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-05 10:33:46.738 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-05 10:33:46.740 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-05 10:33:46.741 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-05 10:33:46.741 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-05 10:33:46.742 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 10:33:46.745 [Warning] WUDFPumaDependencyResolver: Could not load dependency: WdfCoInstaller01009.dll
2025-07-05 10:33:46.748 [Warning] WUDFPumaDependencyResolver: Could not load dependency: WUDFUpdate_01009.dll
2025-07-05 10:33:46.752 [Warning] WUDFPumaDependencyResolver: Could not load dependency: winusbcoinstaller2.dll
2025-07-05 10:33:46.753 [Error] WUDFPumaDependencyResolver: Failed to load WUDFPuma.dll. Error code: 0 (0x0)
2025-07-05 10:33:46.754 [Error] WUDFPumaDependencyResolver: Error details: Unknown error code: 0
2025-07-05 10:33:46.754 [Warning] VocomNativeInterop: Failed to load WUDFPuma.dll or function pointers, falling back to modern .NET 8.0 implementation
2025-07-05 10:33:46.754 [Information] VocomNativeInterop: Using modern .NET 8.0 implementation for Vocom communication
2025-07-05 10:33:46.755 [Information] VocomNativeInterop: Initializing HID device detection for Vocom adapters
2025-07-05 10:33:46.755 [Information] VocomNativeInterop: No HID Vocom devices found
2025-07-05 10:33:46.756 [Information] VocomNativeInterop: Initializing serial port detection for Vocom adapters
2025-07-05 10:33:46.756 [Information] VocomNativeInterop: No serial ports found
2025-07-05 10:33:46.758 [Warning] VocomNativeInterop: Failed to initialize modern Vocom communication, no devices found
2025-07-05 10:33:46.759 [Error] VocomNativeInterop: Failed to initialize Vocom driver
2025-07-05 10:33:46.760 [Error] VocomDeviceDriver: Failed to initialize Vocom native interop layer
2025-07-05 10:33:46.760 [Error] PatchedVocomServiceFactory: Failed to initialize Vocom device driver, falling back to dummy implementation
2025-07-05 10:33:46.763 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 10:33:46.764 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 10:33:46.764 [Information] App: Initializing Vocom service
2025-07-05 10:33:46.765 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 10:33:46.765 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 10:33:46.767 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-05 10:33:46.869 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-05 10:33:46.869 [Information] App: Found 1 Vocom devices, attempting to connect to the first one
2025-07-05 10:33:46.871 [Information] DummyVocomService: Connecting to Vocom device Dummy Vocom Device (dummy)
2025-07-05 10:33:47.072 [Information] DummyVocomService: Connected to Vocom device Dummy Vocom Device (dummy)
2025-07-05 10:33:47.072 [Information] App: Connected to Vocom device Dummy Vocom Device
2025-07-05 10:33:47.090 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-05 10:33:47.094 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:33:47.095 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-05 10:33:47.100 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 10:33:47.104 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 10:33:47.104 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 10:33:47.114 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 10:33:47.127 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 10:33:47.144 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 10:33:47.162 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 10:33:47.180 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 10:33:47.210 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 10:33:47.215 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 10:33:47.242 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 10:33:47.244 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 10:33:47.245 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 10:33:47.246 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 10:33:47.246 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 10:33:47.246 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 10:33:47.246 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 10:33:47.247 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 10:33:47.248 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 10:33:47.250 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 10:33:47.251 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 10:33:47.251 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 10:33:47.252 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 10:33:47.252 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 10:33:47.252 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 10:33:47.253 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 10:33:47.253 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 10:33:47.257 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 10:33:47.264 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 10:33:47.265 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 10:33:47.290 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 10:33:47.293 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:33:47.301 [Information] CANRegisterAccess: Read value 0xC2 from register 0x0141 (simulated)
2025-07-05 10:33:47.308 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:33:47.314 [Information] CANRegisterAccess: Read value 0xC8 from register 0x0141 (simulated)
2025-07-05 10:33:47.320 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:33:47.326 [Information] CANRegisterAccess: Read value 0x20 from register 0x0141 (simulated)
2025-07-05 10:33:47.334 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:33:47.340 [Information] CANRegisterAccess: Read value 0x34 from register 0x0141 (simulated)
2025-07-05 10:33:47.346 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:33:47.353 [Information] CANRegisterAccess: Read value 0x1F from register 0x0141 (simulated)
2025-07-05 10:33:47.355 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 10:33:47.359 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 10:33:47.360 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 10:33:47.365 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 10:33:47.366 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 10:33:47.372 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 10:33:47.373 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 10:33:47.373 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 10:33:47.379 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 10:33:47.380 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 10:33:47.380 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 10:33:47.386 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 10:33:47.387 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 10:33:47.391 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 10:33:47.392 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 10:33:47.397 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 10:33:47.398 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 10:33:47.403 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 10:33:47.404 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 10:33:47.409 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 10:33:47.410 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 10:33:47.416 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 10:33:47.418 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 10:33:47.425 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 10:33:47.426 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 10:33:47.431 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 10:33:47.432 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 10:33:47.437 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 10:33:47.438 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 10:33:47.443 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 10:33:47.444 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 10:33:47.449 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 10:33:47.450 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 10:33:47.455 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 10:33:47.456 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 10:33:47.461 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 10:33:47.462 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 10:33:47.467 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 10:33:47.468 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 10:33:47.473 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 10:33:47.474 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 10:33:47.479 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 10:33:47.480 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 10:33:47.485 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 10:33:47.486 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 10:33:47.486 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 10:33:47.492 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 10:33:47.493 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 10:33:47.493 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 10:33:47.493 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:33:47.499 [Information] CANRegisterAccess: Read value 0xF3 from register 0x0141 (simulated)
2025-07-05 10:33:47.504 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:33:47.510 [Information] CANRegisterAccess: Read value 0xB8 from register 0x0141 (simulated)
2025-07-05 10:33:47.511 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 10:33:47.511 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 10:33:47.511 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 10:33:47.512 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 10:33:47.517 [Information] CANRegisterAccess: Read value 0x6F from register 0x0140 (simulated)
2025-07-05 10:33:47.528 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 10:33:47.548 [Information] CANRegisterAccess: Read value 0xC8 from register 0x0140 (simulated)
2025-07-05 10:33:47.558 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 10:33:47.571 [Information] CANRegisterAccess: Read value 0x09 from register 0x0140 (simulated)
2025-07-05 10:33:47.580 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 10:33:47.604 [Information] CANRegisterAccess: Read value 0xC0 from register 0x0140 (simulated)
2025-07-05 10:33:47.613 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 10:33:47.631 [Information] CANRegisterAccess: Read value 0x2E from register 0x0140 (simulated)
2025-07-05 10:33:47.640 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 10:33:47.653 [Information] CANRegisterAccess: Read value 0x82 from register 0x0140 (simulated)
2025-07-05 10:33:47.665 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 10:33:47.680 [Information] CANRegisterAccess: Read value 0x3E from register 0x0140 (simulated)
2025-07-05 10:33:47.682 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 10:33:47.686 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 10:33:47.691 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 10:33:47.692 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 10:33:47.702 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 10:33:47.703 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 10:33:47.704 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 10:33:47.708 [Information] DummyVocomService: Sending data and waiting for response (dummy)
2025-07-05 10:33:47.859 [Information] DummyVocomService: Sent 4 bytes and received 6 bytes response (dummy)
2025-07-05 10:33:47.861 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 10:33:47.861 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 10:33:47.863 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 10:33:47.864 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 10:33:47.874 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 10:33:47.875 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 10:33:47.876 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 10:33:47.886 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 10:33:47.897 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 10:33:47.908 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 10:33:47.919 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 10:33:47.930 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 10:33:47.933 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 10:33:47.934 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 10:33:47.945 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 10:33:47.946 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 10:33:47.947 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 10:33:47.976 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 10:33:48.008 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 10:33:48.019 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 10:33:48.048 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 10:33:48.059 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 10:33:48.070 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 10:33:48.082 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 10:33:48.082 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 10:33:48.092 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 10:33:48.094 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 10:33:48.094 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 10:33:48.095 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 10:33:48.095 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 10:33:48.095 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 10:33:48.095 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 10:33:48.096 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 10:33:48.096 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 10:33:48.096 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 10:33:48.097 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 10:33:48.097 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 10:33:48.097 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 10:33:48.097 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 10:33:48.098 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 10:33:48.098 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 10:33:48.098 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 10:33:48.199 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 10:33:48.199 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 10:33:48.204 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 10:33:48.205 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:33:48.206 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 10:33:48.206 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 10:33:48.206 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:33:48.207 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 10:33:48.207 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 10:33:48.207 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:33:48.208 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 10:33:48.208 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 10:33:48.208 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:33:48.209 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 10:33:48.209 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 10:33:48.210 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-05 10:33:48.227 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-05 10:33:48.228 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-05 10:33:48.234 [Information] BackupService: Initializing backup service
2025-07-05 10:33:48.235 [Information] BackupService: Backup service initialized successfully
2025-07-05 10:33:48.235 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-05 10:33:48.235 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-05 10:33:48.238 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-05 10:33:48.297 [Information] BackupService: Compressing backup data
2025-07-05 10:33:48.372 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-07-05 10:33:48.374 [Information] BackupServiceFactory: Created template for category: Production
2025-07-05 10:33:48.374 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-05 10:33:48.375 [Information] BackupService: Compressing backup data
2025-07-05 10:33:48.378 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (449 bytes)
2025-07-05 10:33:48.378 [Information] BackupServiceFactory: Created template for category: Development
2025-07-05 10:33:48.379 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-05 10:33:48.380 [Information] BackupService: Compressing backup data
2025-07-05 10:33:48.381 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (445 bytes)
2025-07-05 10:33:48.381 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-05 10:33:48.382 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-05 10:33:48.382 [Information] BackupService: Compressing backup data
2025-07-05 10:33:48.383 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (452 bytes)
2025-07-05 10:33:48.383 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-05 10:33:48.384 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-05 10:33:48.384 [Information] BackupService: Compressing backup data
2025-07-05 10:33:48.385 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-07-05 10:33:48.386 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-05 10:33:48.391 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-05 10:33:48.391 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-05 10:33:48.392 [Information] BackupService: Compressing backup data
2025-07-05 10:33:48.393 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (512 bytes)
2025-07-05 10:33:48.393 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-05 10:33:48.394 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-05 10:33:48.395 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-05 10:33:48.408 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 10:33:48.408 [Information] BackupSchedulerService: Created schedules directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Schedules
2025-07-05 10:33:48.421 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 10:33:48.421 [Information] BackupSchedulerService: Schedules file not found: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Schedules\backup_schedules.json
2025-07-05 10:33:48.422 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 10:33:48.423 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-05 10:33:48.423 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-05 10:33:48.423 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-05 10:33:48.425 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-05 10:33:48.426 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-05 10:33:48.434 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-05 10:33:48.435 [Information] App: Flash operation monitor service initialized successfully
2025-07-05 10:33:48.458 [Information] LicensingService: Initializing licensing service
2025-07-05 10:33:48.606 [Information] LicensingService: License information saved successfully
2025-07-05 10:33:48.610 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-05 10:33:48.611 [Information] App: Licensing service initialized successfully
2025-07-05 10:33:48.611 [Information] App: License status: Trial
2025-07-05 10:33:48.612 [Information] App: Trial period: 30 days remaining
2025-07-05 10:33:48.612 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-05 10:33:48.613 [Information] App: Creating default backup schedules
2025-07-05 10:33:48.620 [Information] ECUCommunicationService: Scanning for ECUs
2025-07-05 10:33:48.621 [Information] ECUCommunicationService: Scanning for ECUs on the CAN bus...
2025-07-05 10:33:49.123 [Information] ECUCommunicationService: Scanning for ECUs using SPI protocol...
2025-07-05 10:33:49.463 [Information] ECUCommunicationService: Scanning for ECUs using SCI protocol...
2025-07-05 10:33:49.764 [Information] ECUCommunicationService: Scanning for ECUs using IIC protocol...
2025-07-05 10:33:50.066 [Information] ECUCommunicationService: Found 10 ECUs
2025-07-05 10:33:50.071 [Information] BackupSchedulerService: Creating backup schedule for ECU EMS
2025-07-05 10:33:50.080 [Information] BackupSchedulerService: Saving backup schedules to disk
2025-07-05 10:33:50.188 [Information] BackupSchedulerService: Saved 1 backup schedules to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Schedules\backup_schedules.json
2025-07-05 10:33:50.189 [Information] BackupSchedulerService: Backup schedule created for ECU EMS
2025-07-05 10:33:50.189 [Information] App: Created daily backup schedule for EMS
2025-07-05 10:33:50.190 [Information] BackupSchedulerService: Creating backup schedule for ECU EMS
2025-07-05 10:33:50.191 [Information] BackupSchedulerService: Saving backup schedules to disk
2025-07-05 10:33:50.192 [Information] BackupSchedulerService: Saved 2 backup schedules to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Schedules\backup_schedules.json
2025-07-05 10:33:50.193 [Information] BackupSchedulerService: Backup schedule created for ECU EMS
2025-07-05 10:33:50.193 [Information] App: Created weekly backup schedule for EMS
2025-07-05 10:33:50.522 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 10:33:50.522 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 10:33:50.573 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 10:33:50.574 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 10:33:50.574 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 10:33:50.574 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 10:33:50.575 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 10:33:50.580 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 10:33:50.581 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 10:33:50.593 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 10:33:50.594 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 10:33:50.594 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 10:33:50.605 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 10:33:50.606 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 10:33:50.606 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 10:33:50.606 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 10:33:50.607 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 10:33:50.607 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 10:33:50.607 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 10:33:50.608 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 10:33:50.608 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 10:33:50.608 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 10:33:50.608 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 10:33:50.609 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 10:33:50.609 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 10:33:50.609 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 10:33:50.610 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 10:33:50.610 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 10:33:50.610 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 10:33:50.610 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 10:33:50.625 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 10:33:50.625 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 10:33:50.626 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 10:33:50.626 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:33:50.647 [Information] CANRegisterAccess: Read value 0xF5 from register 0x0141 (simulated)
2025-07-05 10:33:50.647 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 10:33:50.648 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 10:33:50.648 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 10:33:50.656 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 10:33:50.657 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 10:33:50.665 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 10:33:50.666 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 10:33:50.666 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 10:33:50.692 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 10:33:50.693 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 10:33:50.693 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 10:33:50.710 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 10:33:50.711 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 10:33:50.717 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 10:33:50.718 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 10:33:50.724 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 10:33:50.725 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 10:33:50.731 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 10:33:50.732 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 10:33:50.741 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 10:33:50.741 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 10:33:50.747 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 10:33:50.748 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 10:33:50.754 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 10:33:50.755 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 10:33:50.761 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 10:33:50.762 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 10:33:50.769 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 10:33:50.770 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 10:33:50.776 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 10:33:50.777 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 10:33:50.782 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 10:33:50.783 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 10:33:50.789 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 10:33:50.790 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 10:33:50.796 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 10:33:50.797 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 10:33:50.817 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 10:33:50.818 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 10:33:50.861 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 10:33:50.861 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 10:33:50.874 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 10:33:50.874 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 10:33:50.926 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 10:33:50.927 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 10:33:50.927 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 10:33:50.946 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 10:33:50.946 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 10:33:50.947 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 10:33:50.947 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:33:50.961 [Information] CANRegisterAccess: Read value 0xF7 from register 0x0141 (simulated)
2025-07-05 10:33:50.967 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:33:50.975 [Information] CANRegisterAccess: Read value 0xB3 from register 0x0141 (simulated)
2025-07-05 10:33:50.981 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:33:50.987 [Information] CANRegisterAccess: Read value 0x39 from register 0x0141 (simulated)
2025-07-05 10:33:50.993 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 10:33:50.999 [Information] CANRegisterAccess: Read value 0xEC from register 0x0141 (simulated)
2025-07-05 10:33:50.999 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 10:33:51.000 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 10:33:51.000 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 10:33:51.001 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 10:33:51.007 [Information] CANRegisterAccess: Read value 0xD6 from register 0x0140 (simulated)
2025-07-05 10:33:51.008 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 10:33:51.008 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 10:33:51.009 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 10:33:51.009 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 10:33:51.019 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 10:33:51.020 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 10:33:51.021 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 10:33:51.021 [Information] DummyVocomService: Sending data and waiting for response (dummy)
2025-07-05 10:33:51.172 [Information] DummyVocomService: Sent 4 bytes and received 6 bytes response (dummy)
2025-07-05 10:33:51.173 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 10:33:51.173 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 10:33:51.174 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 10:33:51.174 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 10:33:51.189 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 10:33:51.190 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 10:33:51.190 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 10:33:51.201 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 10:33:51.212 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 10:33:51.223 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 10:33:51.234 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 10:33:51.245 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 10:33:51.246 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 10:33:51.246 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 10:33:51.257 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 10:33:51.258 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 10:33:51.258 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 10:33:51.270 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 10:33:51.281 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 10:33:51.292 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 10:33:51.303 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 10:33:51.314 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 10:33:51.338 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 10:33:51.338 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 10:33:51.339 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 10:33:51.350 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 10:33:51.351 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 10:33:51.352 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 10:33:51.352 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 10:33:51.353 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 10:33:51.353 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 10:33:51.353 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 10:33:51.354 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 10:33:51.354 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 10:33:51.354 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 10:33:51.355 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 10:33:51.355 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 10:33:51.355 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 10:33:51.356 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 10:33:51.356 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 10:33:51.356 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 10:33:51.356 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 10:33:51.457 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 10:33:51.458 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 10:33:51.458 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 10:33:51.459 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:33:51.459 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 10:33:51.459 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 10:33:51.460 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:33:51.460 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 10:33:51.460 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 10:33:51.461 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:33:51.461 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 10:33:51.461 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 10:33:51.462 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 10:33:51.462 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 10:33:51.462 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 10:33:51.513 [Information] BackupService: Initializing backup service
2025-07-05 10:33:51.514 [Information] BackupService: Backup service initialized successfully
2025-07-05 10:33:51.565 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 10:33:51.566 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 10:33:51.610 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\Schedules\backup_schedules.json
2025-07-05 10:33:51.611 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 10:33:51.664 [Information] BackupService: Getting predefined backup categories
2025-07-05 10:33:51.716 [Information] MainViewModel: Services initialized successfully
2025-07-05 10:33:51.729 [Information] MainViewModel: Scanning for Vocom devices
2025-07-05 10:33:51.730 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-05 10:33:51.845 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-05 10:33:51.847 [Information] MainViewModel: Found 1 Vocom device(s)
